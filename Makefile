#project_name=bigdata-bi-analysis
tag=$(shell date +%Y%m%d%H%M)
docker_args=

# harbor地址
harbor_registry=*************:10004
harbor_project_name=bi
env=dev
# 导航页path
env_title=

# 环境判断
ifeq (${env},dev)
	harbor_registry=*************:10004
	env=dev
	docker_args=--add-host=gitlab.bestpay.com.cn:************* --add-host=mavenrepository.bestpay.com.cn:*************
endif

ifeq (${env},preProd)
	harbor_registry=**************:8030
	env=preProd
	docker_args=--add-host=gitlab.bestpay.com.cn:************* --add-host=mavenrepository.bestpay.com.cn:*************
endif

ifeq (${env},prod)
	harbor_registry=***********:8080
	env=prod
	docker_args=--add-host=gitlab.bestpay.com.cn:************* --add-host=mavenrepository.bestpay.com.cn:************
endif

.PHONY: all
all: init deploy.all clean

.PHONY: init
init:
	@echo -e "[\033[31m init \033[0m]"
#	@sed -i "s/^VUE_APP_TITLE.*/$(env_title)/g" .env.production
	@sudo docker run ${docker_args} --rm -it -v ${PWD}:/app -v /data/.m2:/app/.m2 -e LANG=C.UTF-8 ${harbor_registry}/kcs/kcs-reaper-plugin:v1.0.3 /bin/sh -c 'cd /app; sudo mvn -Dmaven.repo.local=.m2 --batch-mode clean install -Dmaven.test.skip=true'
	@echo "init======================>100%"

.PHONY: deploy.all
deploy.all: deploy.bi-analysis deploy.bi-backend deploy.bi-probe deploy.bi-report deploy.bi-thirdparty deploy.bi-database

.PHONY: deploy.bi-analysis
deploy.bi-analysis:
	@echo -e "[\033[31m deploy.bi-analysis \033[0m]"
	@mkdir -p bi-analysis/_deploy
	@cp -a bi-analysis/bi-analysis-service/target/bi-analysis-service-*.jar bi-analysis/_deploy/
	@cp -a bi-analysis/deployments/${env}/. bi-analysis/_deploy/
	@cd bi-analysis/_deploy && sudo docker build -t ${harbor_registry}/${harbor_project_name}/bi-analysis:${tag} .
#	@sudo docker push ${harbor_registry}/${harbor_project_name}/$(project_name):${tag}
#	@sed -i 's/image:.*/image: ${harbor_registry}\/${harbor_project_name}\/$(project_name):${tag}/g' _deploy/$(project_name).yaml
#	@kubectl apply -f _deploy/$(project_name).yaml
	@echo "deploy======================>100%"

.PHONY: deploy.bi-backend
deploy.bi-backend:
	@echo -e "[\033[31m deploy.bi-backend \033[0m]"
	@mkdir -p bi-backend/_deploy
	@cp -a bi-backend/bi-backend-service/target/bi-backend-service-*.jar bi-backend/_deploy/
	@cp -a bi-backend/deployments/${env}/. bi-backend/_deploy/
	@cd bi-backend/_deploy && sudo docker build ${docker_args} -t ${harbor_registry}/${harbor_project_name}/bi-backend:${tag} .
#	@sudo docker push ${harbor_registry}/${harbor_project_name}/$(project_name):${tag}
#	@sed -i 's/image:.*/image: ${harbor_registry}\/${harbor_project_name}\/$(project_name):${tag}/g' _deploy/$(project_name).yaml
#	@kubectl apply -f _deploy/$(project_name).yaml
	@echo "deploy======================>100%"

.PHONY: deploy.bi-probe
deploy.bi-probe:
	@echo -e "[\033[31m deploy.bi-probe \033[0m]"
	@mkdir -p bi-probe/_deploy
	@cp -a bi-probe/bi-probe-service/target/bi-probe-service-*.jar bi-probe/_deploy/
	@cp -a bi-probe/deployments/${env}/. bi-probe/_deploy/
	@cd bi-probe/_deploy && sudo docker build ${docker_args} -t ${harbor_registry}/${harbor_project_name}/bi-probe:${tag} .
#	@sudo docker push ${harbor_registry}/${harbor_project_name}/$(project_name):${tag}
#	@sed -i 's/image:.*/image: ${harbor_registry}\/${harbor_project_name}\/$(project_name):${tag}/g' _deploy/$(project_name).yaml
#	@kubectl apply -f _deploy/$(project_name).yaml
	@echo "deploy======================>100%"

.PHONY: deploy.bi-report
deploy.bi-report:
	@echo -e "[\033[31m deploy.bi-report \033[0m]"
	@mkdir -p bi-report/_deploy
	@cp -a bi-report/bi-report-service/target/bi-report-service-*.jar bi-report/_deploy/
	@cp -a bi-report/deployments/${env}/. bi-report/_deploy/
	@cd bi-report/_deploy && sudo docker build ${docker_args} -t ${harbor_registry}/${harbor_project_name}/bi-report:${tag} .
#	@sudo docker push ${harbor_registry}/${harbor_project_name}/$(project_name):${tag}
#	@sed -i 's/image:.*/image: ${harbor_registry}\/${harbor_project_name}\/$(project_name):${tag}/g' _deploy/$(project_name).yaml
#	@kubectl apply -f _deploy/$(project_name).yaml
	@echo "deploy======================>100%"

.PHONY: deploy.bi-thirdparty
deploy.bi-thirdparty:
	@echo -e "[\033[31m deploy.bi-thirdparty \033[0m]"
	@mkdir -p bi-thirdparty/_deploy
	@cp -a bi-thirdparty/bi-thirdparty-service/target/bi-thirdparty-service-*.jar bi-thirdparty/_deploy/
	@cp -a bi-thirdparty/deployments/${env}/. bi-thirdparty/_deploy/
	@cd bi-thirdparty/_deploy && sudo docker build ${docker_args} -t ${harbor_registry}/${harbor_project_name}/bi-thirdparty:${tag} .
#	@sudo docker push ${harbor_registry}/${harbor_project_name}/$(project_name):${tag}
#	@sed -i 's/image:.*/image: ${harbor_registry}\/${harbor_project_name}\/$(project_name):${tag}/g' _deploy/$(project_name).yaml
#	@kubectl apply -f _deploy/$(project_name).yaml
	@echo "deploy======================>100%"

.PHONY: deploy.bi-database
deploy.bi-database:
	@echo -e "[\033[31m deploy.bi-database \033[0m]"
	@mkdir -p bi-database/_deploy
	@cp -a bi-database/bi-database-service/target/bi-database-service-*.jar bi-database/_deploy/
	@cp -a bi-database/deployments/${env}/. bi-database/_deploy/
	@cd bi-database/_deploy && sudo docker build ${docker_args} -t ${harbor_registry}/${harbor_project_name}/bi-database:${tag} .
#	@sudo docker push ${harbor_registry}/${harbor_project_name}/$(project_name):${tag}
#	@sed -i 's/image:.*/image: ${harbor_registry}\/${harbor_project_name}\/$(project_name):${tag}/g' _deploy/$(project_name).yaml
#	@kubectl apply -f _deploy/$(project_name).yaml
	@echo "deploy======================>100%"

.PHONY: clean
clean:
	@echo -e "[\033[31m clean \033[0m]"
	@sleep 3
	@rm -rf bi-*/_deploy
	@echo "clean======================>100%"
