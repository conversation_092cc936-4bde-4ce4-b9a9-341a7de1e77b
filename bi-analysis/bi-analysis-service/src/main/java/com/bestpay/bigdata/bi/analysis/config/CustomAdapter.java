package com.bestpay.bigdata.bi.analysis.config;

import com.bestpay.bigdata.bi.thirdparty.api.UserInfoService;
import javax.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2021/3/18 9:28
 **/
@Configuration
public class CustomAdapter implements WebMvcConfigurer {

    @Resource
    private UserInfoService userInfoService;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(new CustomInterceptor(userInfoService))
            .addPathPatterns("/**");

    }
}
