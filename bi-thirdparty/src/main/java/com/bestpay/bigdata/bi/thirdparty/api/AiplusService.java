package com.bestpay.bigdata.bi.thirdparty.api;


import com.bestpay.bigdata.bi.common.bean.UserInfoRequest;
import com.bestpay.bigdata.bi.common.common.AIPlusPageable;
import com.bestpay.bigdata.bi.common.dto.AIplusUserInfo;
import com.bestpay.bigdata.bi.common.dto.common.AiPlusUserGroup;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.entity.PermissionInfo;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.response.UserMobileResponse;
import java.util.List;

/**
 * 此类主要作用为封装智加接口，BI侧用户信息功能请放在AiPlusUserService中
 * <AUTHOR>
 * @date 2021/3/30 14:48
 **/
public interface AiplusService {

    /**
     * get current user is or not manager
     *
     * @param userId cookie
     * @return Boolean
     */
    Boolean getCurrentUserPermission(String userId);

    /**
     * get current user in aiplus plat all information,will to expand more info to AIplusUserInfo
     *
     * @param cookieValue cookieValue
     * @return AIplusUserInfo
     */
    AIplusUserInfo getCurrentUserInfo(String cookieValue);

    /**
     * get current user in aiplus plat all information,will to expand more info to AIplusUserInfo
     *
     * @param cookieId cookieId
     * @return UserInfo
     */
    UserInfo getUserInfo(String cookieId);


    /**
     * get user/user list info from ai plus
     *
     * @param request cookieValue,userName
     * @return json format str AIPlus user info
     */
    AIPlusPageable<UserInfo> getAiPlusUserInfo(UserInfoRequest request);

    /**
     * get org list
     *
     * @return json format str AIPlus user info
     */
    List<Org> getOrgList();

    /**
     * 通过邮箱直接从智加获取用户信息
     * @param email
     * @return
     */
    UserInfo getUserInfoByEmailFromAiplus(String email);

    /**
     * 通过邮箱获取用户信息
     *
     * @param email email
     * @return UserInfo
     */
    UserInfo getUserInfoByEmail(String email);

    /**
     * 获取用户权限信息
     * @param cookieValue
     * @param platForm
     * @return
     */
    List<PermissionInfo> getPermissionInfo(String cookieValue, String platForm);

    /**
     * 获取智加用户组信息
     * @return
     */
    List<AiPlusUserGroup> getAiPlusUserGroupList();

    /**根据邮箱查询手机号
     * @param emailList 邮箱
     * @return 手机号
     */
    UserMobileResponse getUserMobileByEmail(List<String> emailList);
}
