package com.bestpay.bigdata.bi.backend.persist;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import com.bestpay.bigdata.bi.backend.audit.dto.BaseAuditDto;
import com.bestpay.bigdata.bi.backend.audit.dto.DownloadAuditDto;
import com.bestpay.bigdata.bi.common.util.PasswordUtil;
import com.bestpay.bigdata.bi.common.api.RedisService;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.config.DataSourceConfig;
import com.bestpay.bigdata.bi.common.dto.ExcelHeaderInfo;
import com.bestpay.bigdata.bi.common.dto.FileDownloadResult;
import com.bestpay.bigdata.bi.common.entity.ColumnName;
import com.bestpay.bigdata.bi.common.entity.FileDownloadInfo;
import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.common.enums.QueryType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.bestpay.bigdata.bi.common.oss.OssServiceFactory;
import com.bestpay.bigdata.bi.common.util.OssUtil;
import com.bestpay.drip.oss.proxy.common.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * The type Persist helper.
 *
 * @param <T> the type parameter
 * <AUTHOR> Xiaobing
 * @date 2022 /3/2 22:38
 */
@Slf4j
public abstract class PersistHelper<T> {

    @Autowired
    private DataSourceConfig dataSourceConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SftpDecryptService sftpDecryptService;

    @Autowired
    private OssServiceFactory ossServiceFactory;

    @Resource
    private PasswordUtil passwordUtil;


    public void setDataSourceConfig(DataSourceConfig dataSourceConfig) {
        this.dataSourceConfig = dataSourceConfig;
    }

    public void setSftpDecryptService(SftpDecryptService sftpDecryptService) {
        this.sftpDecryptService = sftpDecryptService;
    }

    /**
     * Persist to remote.
     *
     * @return the string
     */
    public String persistToRemote(DownloadAuditDto downloadAuditDto) throws Exception {
        long start = System.currentTimeMillis();
        FileType fileType = downloadAuditDto.getFileType();
        FileDownloadResult result = new FileDownloadResult();
        result.setSuccess(true);
        boolean isDownloadExcel = fileType == null || FileType.EXCEL.equals(fileType);
        String downloadResultKey = Constant.REDIS_BIGDATA_BI_DOWNLOAD_PATH + downloadAuditDto.getQueryId();
        String downloadSpecialResultKey = Constant.REDIS_BIGDATA_BI_DOWNLOAD_SPECIAL_PATH + downloadAuditDto.getQueryId() + downloadAuditDto.getFileType().getSuffix() + downloadAuditDto.getNotZip().toString();
        if (!StringUtils.isBlank(downloadAuditDto.getSizeOverflowColumn()) && isDownloadExcel) {
            result.setSuccess(false);
            result.setMessage(downloadAuditDto.getSizeOverflowColumn());
            redisService.setObj(downloadResultKey, result, 1, TimeUnit.DAYS);
            redisService.setObj(downloadSpecialResultKey, result, 1, TimeUnit.DAYS);
            throw new IllegalArgumentException(
                    "persist queryId: " + downloadAuditDto.getQueryId() + " result as excel failed. cause the value of column: " +
                            downloadAuditDto.getSizeOverflowColumn() + " is exceeds the limit");
        }

        String localFilePath = persistFileToLocal(downloadAuditDto);

        String remoteFilePath = OssUtil.upload(ossServiceFactory,
                localFilePath,
                -1,
                Constants.ACCESSED_BY_UNSIGNED_URL);

        //将remoteFilePath保存到 redis中
        result.setDownloadPath(remoteFilePath);
        result.setZipPassword(downloadAuditDto.getZipPassword());
        redisService.setObj(downloadResultKey, result, 1, TimeUnit.DAYS);
        redisService.setObj(downloadSpecialResultKey, result, 1, TimeUnit.DAYS);
        deleteLocalFile(localFilePath);
        long end = System.currentTimeMillis();
        log.info("上传sftp服务器总耗时：{}ms", (end - start));
        return remoteFilePath;
    }

    /**
     * Implement Persist protocol in subclass.
     *
     * @param localFilePath the local file path
     * @return the string
     */
    public abstract String persist(String localFilePath);

    /**
     * Persist file to local path.
     *
     * @return the string
     */
    public String persistFileToLocal(DownloadAuditDto downloadAuditDto) {
        String localPath = dataSourceConfig.getLocalpath();
        FileUtil.mkdir(localPath);
        ExcelHeaderInfo excelHeaderInfo = downloadAuditDto.getExcelHeaderInfo();
        List<ColumnName> columnNameMaps = excelHeaderInfo.getColumnNameMaps();
        List<String> headerList = columnNameMaps.stream().map(ColumnName::getLabel).collect(Collectors.toList());
        String localFilePath = getLocalFilePath(downloadAuditDto, localPath, excelHeaderInfo);

        long start = System.currentTimeMillis();
        List<String> comments = getComments(downloadAuditDto.getTypeCode(), excelHeaderInfo,
                downloadAuditDto.getResults().size(), downloadAuditDto.getUsername());
        downloadAuditDto.setDownloadContent(JSONUtil.toJsonStr(comments)); // record download details

        FileDownloadInfo downloadInfo = new FileDownloadInfo();
        downloadInfo.setDataRow(downloadAuditDto.parseResult(sftpDecryptService));
        downloadInfo.setHeaderRow(headerList);
        downloadInfo.setComments(comments);
        downloadInfo.setFilePath(localFilePath);

        FileType fileType = downloadAuditDto.getFileType() == null ? FileType.EXCEL : downloadAuditDto.getFileType();

        CsvWriter csvWriter = CsvUtil.getWriter(downloadInfo.getFilePath(), CharsetUtil.CHARSET_UTF_8, true);

        BigExcelWriter excelWriter = new BigExcelWriter(10000);
        fileType.download(downloadInfo, true, true, excelWriter, csvWriter);
        long end = System.currentTimeMillis();
        log.info("生成[{}]文件所用时间：{}ms", downloadAuditDto.getFileType(), (end - start));
        if (downloadAuditDto.getNotZip()){
            return localFilePath;
        }else {
            return downloadAuditDto.compressFilePath(localFilePath, passwordUtil);
        }
    }


    /**
     * Delete local file.
     *
     * @param localFilePath the local file path
     */
    private void deleteLocalFile(String localFilePath) {
        FileUtil.del(localFilePath);
    }

    private String getLocalFilePath(BaseAuditDto auditDto, String localPath, ExcelHeaderInfo excelHeaderInfo) {
        String excelName = null;
        if (!Objects.isNull(excelHeaderInfo) && Objects.equals(auditDto.getTypeCode(),
                QueryType.QUERY_REPORT.getCode())) {
            excelName = excelHeaderInfo.getReportName();
        }
        if (Objects.equals(auditDto.getTypeCode(), QueryType.QUERY_PROBE.getCode())) {
            excelName = QueryType.QUERY_PROBE.toString();
        }

        FileType fileType = auditDto.getFileType() == null ? FileType.EXCEL : auditDto.getFileType();
        String localFilePath = StrUtil.builder().append(localPath).append(StrUtil.SLASH).append(excelName).append(
                UUID.randomUUID().toString()).append(fileType.getSuffix()).toString();
        log.info("localFilePath:{}", localFilePath);
        return localFilePath;
    }

    private List<String> getComments(Integer typeCode, ExcelHeaderInfo excelHeaderInfo, int size, String username) {
        List<String> comments = new ArrayList<>();
        if (QueryType.QUERY_REPORT.getCode() == typeCode) {
            comments.add(StrUtil.format("报表名称:{}", excelHeaderInfo.getReportName()));
            comments.add(StrUtil.format("报表说明:{}", excelHeaderInfo.getDescription()));
            comments.add(StrUtil.format("负责人:{}", excelHeaderInfo.getOwner()));
//            comments.add(StrUtil.format("数据日期:{}", excelHeaderInfo.getDataDate()));
            comments.add(StrUtil.format("报表共{}条数据由{}下载于{}", size, username, DateUtil.now()));
        }

        return comments;
    }
}
