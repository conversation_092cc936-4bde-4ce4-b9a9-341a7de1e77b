package com.bestpay.bigdata.bi.backend.config;

import com.bestpay.bigdata.bi.backend.config.datasource.DataSourceHandler;
import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 获取所有引擎连接池处理类
 * <AUTHOR>
 */
@Component
@Slf4j
public class DataSourceHandlerRegister implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;
    private static Map<SQLEngine, DataSourceHandler> dataSourceHandlerMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        log.info("dataSource:loadDataSourceHandler");
        Map<String, DataSourceHandler> beanMap
                = applicationContext.getBeansOfType(DataSourceHandler.class);

        for (DataSourceHandler impl : beanMap.values()) {
            dataSourceHandlerMap.put(impl.engine(), impl);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext){
        this.applicationContext = applicationContext;
    }

    public DataSourceHandler getHandlerByEngine(SQLEngine engine){
        return dataSourceHandlerMap.get(engine);
    }
}