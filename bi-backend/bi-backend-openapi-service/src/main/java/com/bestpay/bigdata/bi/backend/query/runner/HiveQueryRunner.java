package com.bestpay.bigdata.bi.backend.query.runner;


import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.sql.DataSource;

import com.bestpay.bigdata.bi.common.exception.QueryExecutionException;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import org.apache.kyuubi.jdbc.hive.KyuubiStatement;

import static com.bestpay.bigdata.bi.common.enums.CodeEnum.SQL_OPERATION_NOT_PERMIT;

/**
 *
 * <AUTHOR>
 * @date 2021/11/27 16:46
 **/
@NoArgsConstructor
@Slf4j
public class HiveQueryRunner extends BaseQueryRunner {

    public HiveQueryRunner(DataSource hiveDataSource,ApolloRefreshConfig apolloRefreshConfig) {
        super(hiveDataSource,apolloRefreshConfig);
    }

    @Override
    protected void doExecuteInsert(QueryContext queryContext) {
        throw new QueryExecutionException(SQL_OPERATION_NOT_PERMIT);
    }

    /**
     * Before do execute.
     *
     * @param queryContext the query context
     * @param statement    the statement
     * @throws SQLException the sql exception
     */
    @Override
    void beforeDoExecution(QueryContext queryContext, Statement statement) throws SQLException {
        queryContext.setEngineQueryId(getApplicationId());
    }

    /**
     * After do execute.
     *
     * @param queryContext the query context
     * @param statement    the statement
     * @throws SQLException the sql exception
     */
    @Override
    void afterDoExecution(QueryContext queryContext, Statement statement) throws SQLException {

    }


    /**
     * Do execute data query language(DQL).
     *
     * @param queryContext the query context
     * @throws SQLException the sql exception
     */
    @Override
    protected void doExecuteDql(QueryContext queryContext) throws SQLException {
        KyuubiStatement hiveStatement = getConcreteStatement(super.statement,KyuubiStatement.class);
        hiveStatement.setQueryTimeout(apolloRefreshConfig.getStatementHiveQueryTimeoutSeconds());
        try (ResultSet rs = hiveStatement.executeQuery(queryContext.getSql())) {
            processQueryResult(queryContext, rs);
        }
    }


    private String getApplicationId() {
        try {
            ResultSet resultSet = statement.executeQuery("SELECT engine_id()");
            if (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (Exception e) {
            log.error("获取 kyuubi application id happen error", e);
        }
        return null;
    }

}
