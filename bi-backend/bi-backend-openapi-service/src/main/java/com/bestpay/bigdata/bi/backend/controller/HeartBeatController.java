package com.bestpay.bigdata.bi.backend.controller;

import com.bestpay.bigdata.bi.common.constant.RestApiPathConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/3/30 15:22
 **/
@RestController
@RequestMapping(RestApiPathConstant.HEART_BEAT)
@Api(value = "心跳检测", tags = "心跳检测")
public class HeartBeatController {

    @GetMapping
    @ApiOperation(httpMethod = "GET", value = "心跳检测接口", notes = "心跳检测接口")
    public ResponseEntity<String> heartBeat() {
        return ResponseEntity.ok("OK");
    }

}
