package com.bestpay.bigdata.bi.backend.cache;

import com.bestpay.bigdata.bi.common.api.RedisService;
import com.bestpay.bigdata.bi.common.bean.CachedQueryResult;
import com.bestpay.bigdata.bi.common.common.ResultTypeEnum;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:30
 **/
@Component("redisCache")
public class RedisCache implements  Cache<CachedQueryResult>{

    /**
     * Because the queryContext and queryResult caches are not set at the same time,
     * in order to solve the problem that queryContext exists but queryResult doesn't
     * , add a gap time to the queryResult cache
     */
    private static final Integer GAP = 1;

    @Resource
    private RedisService redisService;
    @Resource
    private ApolloRefreshConfig apolloRefreshConfig;

    @Override
    public boolean isHitCache(String cacheKey) {
        return  redisService.hashKey(cacheKey);
    }

    @Override
    public Optional<CachedQueryResult> getCacheData(String cacheKey) {
        return redisService.getObj(cacheKey);
    }

    @Override
    public void clearCacheData(String cacheKey) {
        redisService.del(cacheKey);
    }

    @Override
    public void refreshExpiredTime(String cacheKey) {
        redisService.expire(cacheKey,getExpiredTime(),TimeUnit.MINUTES);
    }

    /**
     * cache type
     *
     * @return CacheTypeEnum
     */
    @Override
    public ResultTypeEnum getResultType()
    {
        return ResultTypeEnum.DATA;
    }

    /**
     * cache data
     *
     * @param queryContext
     */
    @Override
    public void cacheResultIfSucceed(QueryContext queryContext)
    {
        CachedQueryResult cachedQueryResult = new CachedQueryResult(queryContext);
        queryContext.setResultCacheKey(cachedQueryResult.getCacheKey());
        cacheData(cachedQueryResult.getCacheKey(), cachedQueryResult.dataToBeCached());
    }

    private void cacheData(String cacheKey, CachedQueryResult data) {
        redisService.setObj(cacheKey,data, getExpiredTime(), TimeUnit.MINUTES);
    }

    private Integer getExpiredTime() {
        return apolloRefreshConfig.getResultMinutes()+GAP;
    }
}
