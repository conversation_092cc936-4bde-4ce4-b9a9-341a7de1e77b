package com.bestpay.bigdata.bi.backend.query.runner;

import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.exception.QueryExecutionException;

/**
 * The interface Query.
 *
 * <AUTHOR>
 * @date 2021 /11/27 16:41
 */
public interface Query {


    /**
     * Run query.
     *
     * @param queryContext the query context
     * @throws QueryExecutionException the query execution exception
     */
    void runQuery(QueryContext queryContext) throws QueryExecutionException;

    /**
     * Cancel query.
     *
     * @param queryId the query id
     * @throws QueryExecutionException the query execution exception
     */
    void cancelQuery(String queryId) throws QueryExecutionException;

}
