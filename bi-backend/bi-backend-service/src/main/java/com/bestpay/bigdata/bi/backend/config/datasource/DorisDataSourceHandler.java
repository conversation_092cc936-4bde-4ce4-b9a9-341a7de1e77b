package com.bestpay.bigdata.bi.backend.config.datasource;

import com.bestpay.bigdata.bi.backend.query.runner.DorisQueryRunner;
import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import org.apache.commons.dbcp2.PoolingConnection;
import org.apache.commons.dbcp2.PoolingDataSource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023-06-28-9:37
 */
@Component
public class DorisDataSourceHandler implements DataSourceHandler {

    @Override
    public BiDataSourceWrapper getDataSourceWrapper(Long dataSourceId, String config) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(config), "dorisConfig must not be empty");
        PoolingDataSource<PoolingConnection> poolingDataSource = generatePoolingDataSource(config);
        BiDataSourceWrapper biDataSourceWrapper = new BiDataSourceWrapper();
        biDataSourceWrapper.setDataSourceId(dataSourceId);
        biDataSourceWrapper.setPoolingDataSource(poolingDataSource);
        biDataSourceWrapper.setEngineName(SQLEngine.DORIS.getEngineName());
        biDataSourceWrapper.setRunnerClass(DorisQueryRunner.class);
        return biDataSourceWrapper;
    }

    @Override
    public SQLEngine engine() {
        return SQLEngine.DORIS;
    }
}
