package com.bestpay.bigdata.bi.backend.config;

import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.jmx.ThreadPoolExecutorMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;
import java.util.Map;
import java.util.concurrent.Executor;

@Configuration
@Slf4j
public class QueryStatisticExecutorConfig {

    @Autowired
    private ApolloRefreshConfig apolloRefreshConfig;

    private static final String OBJECT_NAME = "com.bestpay.bigdata.bi:name=QueryStatisticExecutorThreadPool";
    private static final String CORE_POOL_SIZE_KEY = "executorPoolSize";
    private static final String MAX_POOL_SIZE_KEY = "executorMaxPoolSize";
    private static final String POOL_CAPACITY_KEY = "executorQueueCapacity";
    private static final String KEEP_ALIVE_KEY = "executorKeepAlive";

    private static final int DEFAUL_CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    private static final int DEFAUL_MAX_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 4;
    private static final int DEFAUL_POOL_CAPACITY = 100;
    private static final int DEFAUL_KEEP_ALIVE = 60;

    @Bean(name = "queryStatisticExecutor")
    public Executor queryStatisticExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        try {
            Map<String, String> queryStatisticMetricConfig = apolloRefreshConfig.getQueryStatisticMetricConfig();

            int executorPoolSize = parseOrDefault(queryStatisticMetricConfig.get(CORE_POOL_SIZE_KEY), DEFAUL_CORE_POOL_SIZE);
            int executorMaxPoolSize = parseOrDefault(queryStatisticMetricConfig.get(MAX_POOL_SIZE_KEY),DEFAUL_MAX_POOL_SIZE);
            int executorQueueCapacity = parseOrDefault(queryStatisticMetricConfig.get(POOL_CAPACITY_KEY), DEFAUL_POOL_CAPACITY);
            int executorKeepAlive = parseOrDefault(queryStatisticMetricConfig.get(KEEP_ALIVE_KEY), DEFAUL_KEEP_ALIVE);

            log.info("Configuring QueryStatisticExecutor: poolSize={}, maxPoolSize={}, queueCapacity={}, keepAlive={}",
                    executorPoolSize, executorMaxPoolSize, executorQueueCapacity, executorKeepAlive);

            executor.setCorePoolSize(executorPoolSize);
            executor.setMaxPoolSize(executorMaxPoolSize);
            executor.setQueueCapacity(executorQueueCapacity);
            executor.setKeepAliveSeconds(executorKeepAlive);
            executor.setThreadNamePrefix("QueryStatistic-");
            executor.initialize();

            registerThreadPoolMBean(executor);

        } catch (Exception e) {
            log.error("Failed to configure QueryStatisticExecutor. Using default configuration.", e);

            executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2);
            executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 4);
            executor.setQueueCapacity(100);
            executor.setKeepAliveSeconds(60);
            executor.setThreadNamePrefix("QueryStatistic-");
            executor.initialize();
        }

        return executor;
    }

    private void registerThreadPoolMBean(ThreadPoolTaskExecutor executor) {
        try {
            MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();
            ObjectName objectName = new ObjectName(OBJECT_NAME);

            if (!mBeanServer.isRegistered(objectName)) {
                ThreadPoolExecutorMetrics metrics = new ThreadPoolExecutorMetrics(executor.getThreadPoolExecutor());
                mBeanServer.registerMBean(metrics, objectName);
                log.info("ThreadPool MBean registered: {}", OBJECT_NAME);
            }
        } catch (Exception e) {
            log.error("Failed to register ThreadPool MBean: {}", OBJECT_NAME, e);
        }
    }

    private int parseOrDefault(String value, int defaultValue) {
        try {
            if (value != null && !value.isEmpty()) {
                return Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            log.warn("Failed to parse value: '{}'. Using default: {}", value, defaultValue, e);
        }
        return defaultValue;
    }
}
