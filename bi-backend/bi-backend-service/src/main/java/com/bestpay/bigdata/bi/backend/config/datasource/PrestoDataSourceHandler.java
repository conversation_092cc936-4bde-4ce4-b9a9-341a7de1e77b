package com.bestpay.bigdata.bi.backend.config.datasource;

import com.bestpay.bigdata.bi.backend.query.runner.PrestoQueryRunner;
import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import org.apache.commons.dbcp2.PoolingConnection;
import org.apache.commons.dbcp2.PoolingDataSource;
import org.springframework.stereotype.Component;


/**
 * Presto
 * <AUTHOR>
 */
@Component
public class PrestoDataSourceHandler implements DataSourceHandler {

  @Override
  public BiDataSourceWrapper getDataSourceWrapper(Long dataSurceId, String config) {
    Preconditions.checkArgument(!Strings.isNullOrEmpty(config), "prestoOneConfig must not be empty");
    PoolingDataSource<PoolingConnection> poolingDataSource = generatePoolingDataSource(config);
    BiDataSourceWrapper biDataSourceWrapper = new BiDataSourceWrapper();
    biDataSourceWrapper.setDataSourceId(dataSurceId);
    biDataSourceWrapper.setPoolingDataSource(poolingDataSource);
    biDataSourceWrapper.setEngineName(SQLEngine.PRESTO.getEngineName());
    biDataSourceWrapper.setRunnerClass(PrestoQueryRunner.class);
    return biDataSourceWrapper;
  }

  @Override
  public SQLEngine engine() {
    return SQLEngine.PRESTO;
  }
}
