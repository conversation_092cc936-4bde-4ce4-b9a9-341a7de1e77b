package com.bestpay.bigdata.bi.backend.audit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import com.bestpay.bigdata.bi.backend.audit.dto.BaseAuditDto;
import com.bestpay.bigdata.bi.backend.audit.dto.QueryAuditDto;
import com.bestpay.bigdata.bi.common.util.LogTraceIdGenerator;
import com.bestpay.bigdata.bi.database.api.common.QueryStatService;
import com.bestpay.bigdata.bi.database.bean.QueryStat;
import java.util.Date;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * all 即系查询，报表|仪表板查询等
 * <AUTHOR>
 * @date 2022/3/1 10:25
 **/
@Component
public class QueryAudit implements Audit {

    private static final Logger logger = LoggerFactory.getLogger(QueryAudit.class);


    @Resource
    private QueryStatService queryStatService;

    public void saveAudit(QueryAuditDto queryAuditVo){
        QueryStat queryStat = new QueryStat();
        BeanUtil.copyProperties(queryAuditVo, queryStat);
        queryStat.setQueryTimeMillis(queryAuditVo.getOperationTimeMillis());
        queryStat.setQueryStartMillis(new DateTime(queryAuditVo.getStartMillis()));
        queryStat.setQueryFinishMillis(new DateTime(queryAuditVo.getFinishMillis()));
        queryStat.setTraceId(MDC.get(LogTraceIdGenerator.TRACE_ID));
        queryStat.setCreatedBy(queryAuditVo.getUsername());
        queryStat.setCreatedAt(new Date());
        queryStat.setUpdatedBy(queryAuditVo.getUsername());
        queryStat.setUpdatedAt(new Date());
        queryStatService.insert(queryStat);
    }

    @Override
    public void audit(BaseAuditDto auditDto) {
        if (!supports(auditDto.getAuditType())) {
            logger.debug("this QueryAudit can't support the user:{} operation:{}  auditType:{}",
                    auditDto.getUsername(), auditDto.getTypeCode(), auditDto.getAuditType());
            return;
        }

        QueryAuditDto queryAuditVo=(QueryAuditDto)auditDto;
        saveAudit(queryAuditVo);
    }

    @Override
    public boolean supports(AuditType auditType) {
        return AuditType.QUERY.equals(auditType) || AuditType.DOWNLOAD.equals(auditType);
    }

}
