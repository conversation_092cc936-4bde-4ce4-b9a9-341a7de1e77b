package com.bestpay.bigdata.bi.backend.aspect;

import com.bestpay.bigdata.bi.backend.jmx.QueryStatisticManager;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

@Aspect
@Component
@Slf4j
public class QueryStatisticAspect {

    @Autowired
    private QueryStatisticManager queryStatisticManager;

    @Pointcut("execution(* com.bestpay.bigdata.bi.backend.api.impl.QueryServiceImpl.queryStarted(..))")
    public void queryStartPointcut() {
    }

    @Pointcut("execution(* com.bestpay.bigdata.bi.backend.api.impl.QueryServiceImpl.queryFinished(..))")
    public void queryFinishPointcut() {
    }

    @After("queryStartPointcut()")
    public void queryStartAfter(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            QueryContext queryContext = (QueryContext) args[0];
            queryStatisticManager.handleQueryStart(queryContext);

        }catch (Exception e) {
            log.error("QueryStatisticMetric exception in queryStartAfter: {}", e.getMessage(), e);
        }
    }

    @After("queryFinishPointcut()")
    public void queryFinishAfter(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            QueryContext queryContext = (QueryContext) args[0];
            queryStatisticManager.handleQueryFinish(queryContext);

        } catch (Exception e) {
            log.error("QueryStatisticMetric exception in queryFinishAfter: {}", e.getMessage(), e);
        }
    }
}