package com.bestpay.bigdata.bi.backend.api.impl;

import org.springframework.beans.factory.annotation.Autowired;
import com.alibaba.dubbo.config.annotation.Service;
import com.bestpay.bigdata.bi.backend.api.QueryService;
import com.bestpay.bigdata.bi.backend.api.QueryTaskCancelService;

/**
 * @author: laiyao
 * @date: 2022/04/26
 * the proxy of queryService
 */
@Service
public class QueryTaskCancelServiceImpl implements QueryTaskCancelService {

    @Autowired
    private QueryService queryService;


    @Override
    public Boolean executeCancel(String queryId) {
        return queryService.executeCancel(queryId);
    }
}
