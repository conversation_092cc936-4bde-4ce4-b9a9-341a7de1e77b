package com.bestpay.bigdata.bi.backend.config.datasource;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import org.apache.commons.dbcp2.*;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

/**
 * <AUTHOR>
 */
public interface DataSourceHandler {

    /**
     * 获取数据源连接池
     * @param config
     * @return
     */
    BiDataSourceWrapper getDataSourceWrapper(Long dataSourceId, String config);

    /**
     * 引擎类型
     * @return
     */
    SQLEngine engine();

    default PoolingDataSource<PoolingConnection> generatePoolingDataSource(String config) {
        DataSource dataSource = JSONUtil.toBean(config, DataSource.class);
        ConnectionFactory connectionFactory = new DriverManagerConnectionFactory(dataSource.getJdbcUrl(),
                dataSource.getUserName(), dataSource.getPassWord());
        PoolableConnectionFactory poolableConnectionFactory = new PoolableConnectionFactory(connectionFactory, null);
        poolableConnectionFactory.setValidationQuery(dataSource.getValidationQuery());
        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(dataSource.getMaxTotal());
        poolConfig.setMaxWaitMillis(dataSource.getMaxWaitMillis());
        poolConfig.setTestOnBorrow(dataSource.getTestOnBorrow());
        poolConfig.setJmxNamePrefix(dataSource.getJmxBeanName());
        GenericObjectPool connectionPool = new GenericObjectPool(poolableConnectionFactory, poolConfig);
        return new PoolingDataSource<PoolingConnection>(connectionPool);
    }
}
