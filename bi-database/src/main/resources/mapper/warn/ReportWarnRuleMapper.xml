<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.warn.ReportWarnRuleMapper">

    <resultMap type="com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo" id="ReportWarnRuleMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="warnCode" column="warn_code" jdbcType="VARCHAR"/>
        <result property="conditionDesc" column="condition_desc" jdbcType="VARCHAR"/>
        <result property="expectationDesc" column="expectation_desc" jdbcType="BIGINT"/>
        <result property="statusCode" column="status_code" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="query" resultMap="ReportWarnRuleMap">
        select *
        from t_report_warn_rule
        <where>
            status_code!=9
            <if test="null != warnCodeList and warnCodeList.size > 0">
                and warn_code in
                <foreach collection="warnCodeList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="batchInsert" parameterType="com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo">
        INSERT INTO t_report_warn_rule (
        `warn_code`,
        `condition_desc`,
        `expectation_desc`,
        created_by,
        updated_by)
        VALUES
        <foreach item="item" collection="list" separator=",">
        (#{item.warnCode},
        #{item.conditionDesc},
        #{item.expectationDesc},
        #{item.createdBy},
        #{item.updatedBy})
        </foreach>
    </insert>



    <insert id="batchShadeInsert" parameterType="com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo">
        INSERT INTO t_report_warn_shade_rule (
        `id`,
        `warn_code`,
        `condition_desc`,
        `expectation_desc`,
        created_by,
        updated_by)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (
            #{item.id},
            #{item.warnCode},
            #{item.conditionDesc},
            #{item.expectationDesc},
            #{item.createdBy},
            #{item.updatedBy})
        </foreach>
    </insert>


    <!--通过实体作为筛选条件查询-->
    <select id="queryShade" resultMap="ReportWarnRuleMap">
        select *
        from t_report_warn_shade_rule
        <where>
            status_code!=9
            <if test="null != warnCodeList and warnCodeList.size > 0">
                and warn_code in
                <foreach collection="warnCodeList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


    <!--通过主键修改数据-->
    <update id="update">
        update t_report_warn_rule
        <set>
            <if test="statusCode != null">
                status_code = #{statusCode},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
        </set>
        where status_code!=9 and warn_code = #{warnCode}
    </update>

    <!--通过主键修改数据-->
    <update id="updateById">
        update t_report_warn_rule
        <set>
            <if test="conditionDesc != null">
                condition_desc = #{conditionDesc},
            </if>
            <if test="expectationDesc != null">
                expectation_desc = #{expectationDesc},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findOriginal" resultMap="ReportWarnRuleMap">
        select *
        from t_report_warn_rule
        <where>
            status_code!=9
            <if test="null != warnCodeList and warnCodeList.size > 0">
                and warn_code in
                <foreach collection="warnCodeList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findBak" resultMap="ReportWarnRuleMap">
        select *
        from t_report_warn_rule_bak
        where
            status_code!=9
    </select>

    <!-- 批量插入到备份表 -->
    <insert id="batchInsertBak" parameterType="java.util.List">
        INSERT INTO t_report_warn_rule_bak (
            id,
            warn_code,
            condition_desc,
            expectation_desc,
            status_code,
            created_at,
            created_by,
            updated_at,
            updated_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.warnCode},
                #{item.conditionDesc},
                #{item.expectationDesc},
                #{item.statusCode},
                #{item.createdAt},
                #{item.createdBy},
                #{item.updatedAt},
                #{item.updatedBy}
            )
        </foreach>
    </insert>

</mapper>