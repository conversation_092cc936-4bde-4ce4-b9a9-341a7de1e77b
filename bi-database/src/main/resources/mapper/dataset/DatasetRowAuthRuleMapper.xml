<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.dataset.DatasetRowAuthRuleMapper">

  <resultMap id="DatasetRowAuthRuleResult" type="com.bestpay.bigdata.bi.database.dao.dataset.DatasetRowAuthRuleDo">
    <id property="id" column="id" />
    <result property="datasetId" column="datasetId" />
    <result property="ruleType" column="rule_type" />
    <result property="ruleName" column="rule_name" />
    <result property="userType" column="user_type" />
    <result property="authUsers" column="auth_users" />
    <result property="value" column="value" />
    <result property="statusCode" column="status_code" />
    <result property="createdAt" column="created_at" />
    <result property="createdBy" column="created_by" />
    <result property="updatedAt" column="updated_at" />
    <result property="updatedBy" column="updated_by" />
  </resultMap>

  <select id="query" resultMap="DatasetRowAuthRuleResult">
    select *
    from t_dataset_row_auth_rule
    <where>
      1=1 and status_code!=9
      <if test="datasetId != null and datasetId != ''">
        and datasetId = #{datasetId}
      </if>
    </where>
    order by updated_at desc
  </select>

  <select id="queryById" resultMap="DatasetRowAuthRuleResult">
    select *
    from t_dataset_row_auth_rule
    <where>
      1=1 and status_code!=9
      <if test="id != null and id != ''">
        and id = #{id}
      </if>
    </where>
    order by updated_at desc
  </select>

  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO t_dataset_row_auth_rule (datasetId,
                                         rule_type,
                                         user_type,
                                         rule_name,
                                         auth_users,
                                         value,
                                         status_code,
                                         created_at,
                                         created_by,
                                         updated_at,
                                         updated_by)
    VALUES (#{datasetId},
            #{ruleType},
            #{userType},
            #{ruleName},
            #{authUsers},
            #{value},
            #{statusCode},
            #{createdAt},
            #{createdBy},
            #{updatedAt},
            #{updatedBy})
  </insert>

  <delete id="updateStatus">
    update t_dataset_row_auth_rule set status_code = #{statusCode} WHERE id = #{id}
  </delete>

  <select id="queryAll" resultMap="DatasetRowAuthRuleResult">
    select *
    from t_dataset_row_auth_rule
    where status_code != 9
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO t_dataset_row_auth_rule_bak (
         id,
         datasetId,
         rule_type,
         user_type,
         rule_name,
         auth_users,
         value,
         status_code,
         created_at,
         created_by,
         updated_at,
         updated_by)
    VALUES
    <foreach collection="rules" item="rule" separator=",">
      (#{rule.id},
       #{rule.datasetId},
       #{rule.ruleType},
       #{rule.userType},
       #{rule.ruleName},
       #{rule.authUsers},
       #{rule.value},
       #{rule.statusCode},
       #{rule.createdAt},
       #{rule.createdBy},
       #{rule.updatedAt},
       #{rule.updatedBy})
    </foreach>
  </insert>


</mapper>