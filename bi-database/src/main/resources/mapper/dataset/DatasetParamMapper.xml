<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.dataset.DatasetParamMapper">

    <resultMap type="com.bestpay.bigdata.bi.database.dao.dataset.DatasetParamDo" id="map">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="datasetCode" column="dataset_code" jdbcType="VARCHAR"/>
        <result property="cnName" column="cn_name" jdbcType="VARCHAR"/>
        <result property="enName" column="en_name" jdbcType="VARCHAR"/>
        <result property="showTypeName" column="showtype_name" jdbcType="VARCHAR"/>
        <result property="filedRange" column="filed_range" jdbcType="VARCHAR"/>
        <result property="defaultValue" column="default_value" jdbcType="VARCHAR"/>
        <result property="valueList" column="value_list" jdbcType="VARCHAR"/>
        <result column="date_type" property="dateType" jdbcType="VARCHAR"/>
        <result column="filter_type" property="filterType" jdbcType="VARCHAR"/>
        <result column="time_type" property="timeType" jdbcType="VARCHAR"/>
        <result column="default_type" property="defaultType" jdbcType="VARCHAR"/>
        <result column="include_current" property="includeCurrent" jdbcType="INTEGER"/>
        <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="statusCode" column="status_code" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="query" resultMap="map">
        select *
        from t_dataset_param
        <where>
            status_code!=9
            <if test="datasetCode != null">
                and dataset_code = #{datasetCode}
            </if>
            <if test="datasetCodeList != null">
                and dataset_code in
                <foreach collection="datasetCodeList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_dataset_param (dataset_code,
        cn_name,
        en_name,
        showtype_name,
        filed_range,
        value_list,
        default_value,
        date_type,
        filter_type,
        time_type,
        default_type,
        include_current,
        uuid,
        status_code,
        created_by,
        created_at,
        updated_by,
        updated_at)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.datasetCode},
             #{item.cnName},
             #{item.enName},
             #{item.showTypeName},
             #{item.filedRange},
             #{item.valueList},
             #{item.defaultValue},
             #{item.dateType},
             #{item.filterType},
             #{item.timeType},
             #{item.defaultType},
             #{item.includeCurrent},
             #{item.uuid},
             #{item.statusCode},
             #{item.createdBy},
             #{item.createdAt},
             #{item.updatedBy},
             #{item.updatedAt})
        </foreach>
    </insert>

    <update id="update" parameterType="com.bestpay.bigdata.bi.database.dao.dataset.DatasetParamDo">
        update t_dataset_param
        <set>
            <if test="statusCode != null">
                `status_code` = #{statusCode},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                `updated_by` = #{updatedBy},
            </if>

            <if test="updatedAt != null">
                `updated_at` = #{updatedAt}
            </if>
        </set>
        where status_code!=9 and dataset_code = #{datasetCode}
    </update>

    <update id="updateById" parameterType="com.bestpay.bigdata.bi.database.dao.dataset.DatasetParamDo">
        update t_dataset_param
        <set>

            <if test="filterType != null">
                `filter_type` = #{filterType},
            </if>
            <if test="dateType != null">
                `date_type` = #{dateType},
            </if>

            <if test="defaultValue != null">
                `default_value` = #{defaultValue},
            </if>

            <if test="timeType != null">
                `time_type` = #{timeType}
            </if>

        </set>
        where id!= #{id}
    </update>
</mapper>