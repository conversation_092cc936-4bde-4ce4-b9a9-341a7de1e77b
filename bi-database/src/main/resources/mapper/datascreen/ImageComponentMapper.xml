<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bestpay.bigdata.bi.database.mapper.datascreen.ImageComponentMapper">

  <resultMap id="ImageComponentResultMap" type="com.bestpay.bigdata.bi.database.dao.datascreen.ImageComponentDO">
    <id property="id" column="id"/>
    <result property="componentConfig" column="component_config"/>

    <result property="statusCode" column="status_code"/>
    <result property="createdAt" column="created_at"/>
    <result property="createdBy" column="created_by"/>
    <result property="updatedAt" column="updated_at"/>
    <result property="updatedBy" column="updated_by"/>
  </resultMap>


  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    <selectKey resultType="Long" keyProperty="id" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    INSERT INTO t_image_component (component_config,
                                   created_by,
                                   updated_by)
    VALUES (#{componentConfig},
        #{createdBy},
        #{updatedBy})
  </insert>

  <update id="update" parameterType="com.bestpay.bigdata.bi.database.dao.datascreen.ImageComponentDO">
    UPDATE t_image_component
    <set>
      <if test="componentConfig != null">
        component_config = #{componentConfig},
      </if>

      <if test="statusCode != null">
        status_code = #{statusCode},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy}
      </if>
    </set>
    <where>
      status_code != 9
      <if test="id != null">
        AND id = #{id}
      </if>
    </where>
  </update>

  <select id="findById" resultMap="ImageComponentResultMap">
    select * from t_image_component where  status_code !=9 and id=#{id,jdbcType=BIGINT}
  </select>

</mapper>
