package com.bestpay.bigdata.bi.database.mapper.subscribe;

import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDTO;
import java.util.List;

import com.bestpay.bigdata.bi.database.bean.subscribe.SubDTO;
import org.apache.ibatis.annotations.Param;

/**
 * @author: den<PERSON><PERSON><PERSON>
 * @date: 2022/12/20
 */
public interface ObjectSubscribeMapper {
    int insert(ObjectSubScribeDO dashboardSubScribeDO);

    ObjectSubScribeDO getById(Long id);

    ObjectSubScribeDO getByCode(String subCode);

    int updateById(ObjectSubScribeDO dashboardSubScribeDO);

    int updateByDashboardId(ObjectSubScribeDO dashboardSubScribeDO);

    List<ObjectSubScribeDO> queryAll();

    List<ObjectSubScribeDO> queryByTaskName(@Param("taskName") String taskName, @Param("id") Long id);

    List<ObjectSubScribeDO> queryByObjSubDTO(@Param("subScribeDTO") ObjectSubScribeDTO subScribeDTO);

    /**
     * 查询最大的订阅code
     * @return  最大的订阅code
     */
    String queryMaxSubCode();

    List<SubDTO> getSubscriptionTaskList(@Param("objectSubScribeDTO") ObjectSubScribeDTO subScribeDTO);

    List<ObjectSubScribeDO> queryBySubType(@Param("subType") String subType);

    List<ObjectSubScribeDO> findOriginal(@Param("subScribeDTO") ObjectSubScribeDTO subScribeDTO);

    List<ObjectSubScribeDO> findBak();

    void batchInsert(@Param("subscribeList") List<ObjectSubScribeDO> subscribeList);
}
