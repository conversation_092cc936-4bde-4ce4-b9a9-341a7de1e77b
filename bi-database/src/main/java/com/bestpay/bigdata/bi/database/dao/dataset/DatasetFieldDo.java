package com.bestpay.bigdata.bi.database.dao.dataset;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-07-04-15:39
 */
@Data
public class DatasetFieldDo implements Serializable {
    private static final long serialVersionUID = -88089201981327986L;

    /**主键*/
    private Long id;

    /**序号*/
    private Integer index;

    /**数据源类型*/
    private String datasourceType;

    /**数据源名称*/
    private String datasourceName;

    /**库名*/
    private String databaseName;

    /**表名*/
    private String tableName;

    /**字段英文名*/
    private String fieldEnName;

    /**字段中文名*/
    private String fieldCnName;

    /**字段描述*/
    private String fieldComment;

    /**字段原始类型*/
    private String fieldOriginType;

    /**状态*/
    private Integer statusCode;

    private Date createdAt;

    private String createdBy;

    private Date updatedAt;

    private String updatedBy;
}
