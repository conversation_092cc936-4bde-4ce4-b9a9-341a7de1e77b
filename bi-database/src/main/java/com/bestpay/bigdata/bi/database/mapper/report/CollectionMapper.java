package com.bestpay.bigdata.bi.database.mapper.report;

import com.bestpay.bigdata.bi.database.bean.Collection;
import com.bestpay.bigdata.bi.database.bean.CollectionResponse;
import java.util.List;

/**
 * (TCollection)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-17 11:33:37
 */
public interface CollectionMapper {
  /**
   * 通过userName 查询收藏列表
   *
   * @param collection 实例对象
   * @return 对象列表
   */
  List<CollectionResponse> queryCollectionList(Collection collection);

  /**
   * 新增数据
   *
   * @param collection 实例对象
   * @return 影响行数
   */
  int insert(Collection collection);

  /**
   * 通过主键删除数据
   *
   * @param id 主键
   * @return 影响行数
   */
  int deleteById(Long id);

}

