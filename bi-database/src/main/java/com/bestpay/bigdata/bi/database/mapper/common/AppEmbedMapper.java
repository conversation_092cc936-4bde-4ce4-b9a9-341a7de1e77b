package com.bestpay.bigdata.bi.database.mapper.common;

import com.bestpay.bigdata.bi.database.bean.EmbedPageQueryDTO;
import com.bestpay.bigdata.bi.database.dao.common.AppEmbedDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用嵌入(AppEmbed)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-09-14 13:58:17
 */
public interface AppEmbedMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AppEmbedDO queryById(Long id);
    /**
     * 取最新数据数据
     *
     * @return 实例对象
     */
    AppEmbedDO queryMaxRecord();

    /**
     * 分页多条件查询
     * @return
     */
    List<AppEmbedDO> pageQuery(EmbedPageQueryDTO pageQueryDTO);

    /**
     * 与pageQueryByMultiCondition搭配使用, total size
     * @return
     */
    Long count(EmbedPageQueryDTO pageQueryDTO);

    /**
     * 新增数据
     *
     * @param appEmbedDO 实例对象
     * @return 影响行数
     */
    Long insert(AppEmbedDO appEmbedDO);


    /**
     * 修改数据
     *
     * @param appEmbedDO 实例对象
     * @return 影响行数
     */
    int update(AppEmbedDO appEmbedDO);

    /**
     * 嵌入页面列表
     * @param embedPageQueryDTO 查询条件。包含 责任人 状态等
     * @return 嵌入页面列表
     */
    List<AppEmbedDO> queryAppEmbedList(@Param("embedPageQueryDTO") EmbedPageQueryDTO embedPageQueryDTO);
}
