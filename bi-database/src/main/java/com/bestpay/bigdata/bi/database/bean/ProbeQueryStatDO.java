package com.bestpay.bigdata.bi.database.bean;

import java.util.Date;
import lombok.Data;

/**
 * 查询对应查询引擎的资源信息(TQueryStatEngineResource)实体类
 *
 * <AUTHOR>
 * @since 2022-01-17 15:09:22
 */
@Data
public class ProbeQueryStatDO {

  private Long id;
  /**
   * 用户
   */
  private String username;
  /**
   * 用户组
   */
  private String userOrg;
  /**
   * 查询开始时间
   */
  private Date queryStartMillis;
  /**
   * 查询结束时间
   */
  private Date queryFinishMillis;
  /**
   * 查询需要的时间
   */
  private Long queryTimeMillis;
  /**
   * 查询的状态（0：提交:1：成功、2：失败:3：取消:4：执行中:5：超时）
   */
  private Integer status;
  /**
   * 查寻的返回信息（一般是报错信息）
   */
  private String message;
  /**
   * 查询引擎名称
   */
  private String engineName;
  /**
   * 集群标识
   */
  private String clusterName;

  /**
   * 数据源类型
   */
  private String datasourceType;
  
  /**
   * 查询语句
   */
  private String sql;
  /**
   * 查询结果记录数
   */
  private Integer resultRowsNum;
  /**
   * 查询结果占用内存或文件大小
   */
  private String resultLength;
  /**
   * 查询引擎返回query id信息
   */
  private String engineQueryId;

  /**
   * query id信息
   */
  private String queryId;

  /**
   * 查询类型编码（0：多维分析 1：数据探查 2：报表中心）
   */
  private Integer typeCode;

  private String traceId;

  private Date createdAt;

  private String createdBy;

  private Date updatedAt;

  private String updatedBy;

}
