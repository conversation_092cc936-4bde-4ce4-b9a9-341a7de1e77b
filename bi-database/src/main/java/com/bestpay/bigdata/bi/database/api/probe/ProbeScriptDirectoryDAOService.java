package com.bestpay.bigdata.bi.database.api.probe;

import com.bestpay.bigdata.bi.database.dao.probe.ProbeScriptDirectoryDo;
import java.util.List;


/**
 * ClassName: ProbeScriptDirectoryService
 * Package: com.bestpay.bigdata.bi.database.api
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/9/18 17:23
 * @Version 1.0
 */
public interface ProbeScriptDirectoryDAOService {

    /**
     * add directory do
     * @param directoryDo do
     * @return probe script directory do with id
     */
    ProbeScriptDirectoryDo insertDb(ProbeScriptDirectoryDo directoryDo);


    /**
     * update directory do
     * @param directoryDo do
     * @return probe script directory do with id
     */
    ProbeScriptDirectoryDo updateDbById(ProbeScriptDirectoryDo directoryDo);


    /**
     * query directory do by id
     * @param id id
     * @return directory do
     */
    ProbeScriptDirectoryDo queryDbById(Long id);

    /**
     * get max dir sort from db
     * @Param createdBy
     * @return max dir sort
     */
    Long getMaxDirSort(String createdBy);


    /**
     * get directory list by name
     * @param createdBy
     * @return list
     */
    List<ProbeScriptDirectoryDo> getDirectoryListByCreatedBy(String createdBy);
}
