package com.bestpay.bigdata.bi.database.api.common;

import com.bestpay.bigdata.bi.database.bean.EmbedPageQueryDTO;
import com.bestpay.bigdata.bi.database.dao.common.AppEmbedDO;
import java.util.List;

/**
 * 应用嵌入(TAppEmbed)表服务接口
 *
 * <AUTHOR>
 * @since 2022-09-14 13:58:24
 */
public interface AppEmbedService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AppEmbedDO queryById(Long id);

    /**
     * 多条件分页查询
     * @return
     */
    List<AppEmbedDO> pageQuery(EmbedPageQueryDTO pageQueryDTO);

    /**
     * 统计总行数, 与pageQueryByMultiCondition搭配使用
     * @return
     */
    Long count(EmbedPageQueryDTO pageQueryDTO);

    /**
     * 取最新数据
     *
     * @return 实例对象
     */
    AppEmbedDO queryMaxRecord();

    /**
     * 新增数据
     *
     * @param appEmbedDO 实例对象
     * @return 实例对象
     */
    AppEmbedDO insert(AppEmbedDO appEmbedDO);

    /**
     * 修改数据
     *
     * @param appEmbedDO 实例对象
     * @return 实例对象
     */
    void update(AppEmbedDO appEmbedDO);

    /**
     * 嵌入页面列表
     * @param embedPageQueryDTO 查询条件。包含 责任人 状态等
     * @return 嵌入页面列表
     */
    List<AppEmbedDO> queryAppEmbedList(EmbedPageQueryDTO embedPageQueryDTO);
}
