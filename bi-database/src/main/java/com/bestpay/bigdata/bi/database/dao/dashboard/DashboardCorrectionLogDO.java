package com.bestpay.bigdata.bi.database.dao.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 仪表板数据订正日志实体类
 * 记录仪表板相关表的UUID订正操作详细信息
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardCorrectionLogDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 批次ID，用于标识一次完整的数据订正操作
     */
    private String batchId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 仪表板ID（如果适用）
     */
    private Long dashboardId;

    /**
     * 数据集ID（如果适用）
     */
    private Long datasetId;

    /**
     * 原始JSON值
     */
    private String originalValue;

    /**
     * 处理后的JSON值
     */
    private String processedValue;

    /**
     * 处理状态：SUCCESS/NO_CHANGE/FAILURE
     */
    private String status;

    /**
     * 错误类型（失败时记录）
     */
    private String errorType;

    /**
     * 错误信息（失败时记录）
     */
    private String errorMessage;

    /**
     * 异常堆栈信息（失败时记录）
     */
    private String stackTrace;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 处理状态常量
     */
    public static class Status {
        public static final String SUCCESS = "SUCCESS";
        public static final String NO_CHANGE = "NO_CHANGE";
        public static final String FAILURE = "FAILURE";
        public static final String VALIDATION_SUCCESS = "VALIDATION_SUCCESS";
        public static final String VALIDATION_FAILURE = "VALIDATION_FAILURE";
    }

    /**
     * 错误类型常量
     */
    public static class ErrorType {
        public static final String JSON_PARSE_ERROR = "JSON_PARSE_ERROR";
        public static final String UUID_MAPPING_ERROR = "UUID_MAPPING_ERROR";
        public static final String DATA_PROCESSING_ERROR = "DATA_PROCESSING_ERROR";
        public static final String DATABASE_UPDATE_ERROR = "DATABASE_UPDATE_ERROR";
        public static final String VALIDATION_ERROR = "VALIDATION_ERROR";
        public static final String UNIQUENESS_ERROR = "UNIQUENESS_ERROR";
        public static final String UNKNOWN_ERROR = "UNKNOWN_ERROR";
    }

    /**
     * 表名常量
     */
    public static class TableName {
        public static final String DASHBOARD_REPORT_CARD = "t_dashboard_report_card";
        public static final String DASHBOARD_FILTER_CARD = "t_dashboard_filter_card";
        public static final String DASHBOARD_INDEX_TEXT_CARD = "t_dashboard_index_text_card";
        public static final String OBJECT_SUBSCRIBE = "t_object_subscribe";
        public static final String REPORT_WARN = "t_report_warn";
        public static final String REPORT_WARN_RULE = "t_report_warn_rule";
    }
}
