package com.bestpay.bigdata.bi.database.mapper.imei;

import com.bestpay.bigdata.bi.database.bean.imei.DelImeiDownloadDTO;
import com.bestpay.bigdata.bi.database.bean.imei.ImeiDownloadDO;
import com.bestpay.bigdata.bi.database.bean.imei.QueryImeiDownloadDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ImeiDownloadMapper {

    int insert(ImeiDownloadDO imeiDO);

    int delete(DelImeiDownloadDTO imeiDTO);

    int update(ImeiDownloadDO imeiDO);

    List<ImeiDownloadDO> query(QueryImeiDownloadDTO queryDTO);

}
