package com.bestpay.bigdata.bi.database.api.dashboard;

import com.bestpay.bigdata.bi.database.bean.dashboard.DashBoardConfigDO;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashBoardConfigDTO;

/**
 * @author:gaodingsong
 * @description:
 * @createTime:2024/5/6 14:34
 * @version:1.0
 */
public interface DashboardConfigService {
    int insert( DashBoardConfigDO config);

    DashBoardConfigDO queryByDashboardId(Long dashboardId);

    int updateStatus(DashBoardConfigDTO config);
}
