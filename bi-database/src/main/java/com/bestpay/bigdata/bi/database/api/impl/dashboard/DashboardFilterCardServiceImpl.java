package com.bestpay.bigdata.bi.database.api.impl.dashboard;


import com.bestpay.bigdata.bi.common.dto.dashboard.FilterCardQueryDTO;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardFilterCardService;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
import com.bestpay.bigdata.bi.database.mapper.dashboard.DashboardFilterCardMapper;

import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
public class DashboardFilterCardServiceImpl implements DashboardFilterCardService {

    @Resource
    private DashboardFilterCardMapper filterCardMapper;

    @Override
    public Long insert(DashboardFilterCardDO filterCardDO){
        filterCardMapper.insert(filterCardDO);
        return filterCardDO.getId();
    }

    @Override
    public DashboardFilterCardDO findById(Long id){
        return filterCardMapper.findById(id);
    }

    @Override
    public List<DashboardFilterCardDO> find(FilterCardQueryDTO cardQueryDTO){
        return filterCardMapper.find(cardQueryDTO);
    }

    @Override
    public void update(Long dashboardId, int statusCode, String updateBy){
        filterCardMapper.update(dashboardId, statusCode, updateBy);
    }

    @Override
    public void updateAll(DashboardFilterCardDO filterCardDO) {
        filterCardMapper.updateAll(filterCardDO);
    }

    @Override
    public List<DashboardFilterCardDO> findOriginal() {
        return filterCardMapper.findOriginal();
    }

    @Override
    public List<DashboardFilterCardDO> findBak() {
        return filterCardMapper.findBak();
    }

    @Override
    public void batchInsert(List<DashboardFilterCardDO> filterCardList) {
        if (filterCardList != null && !filterCardList.isEmpty()) {
            filterCardMapper.batchInsert(filterCardList);
        }
    }
}
