package com.bestpay.bigdata.bi.database.api.datascreen;

import com.bestpay.bigdata.bi.database.dao.datascreen.AxisAndChartFieldDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.ReportComponentDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-29
 */
public interface ReportComponentDaoService {
    Long insert(ReportComponentDO reportComponentDO);

    ReportComponentDO findReportById(Long id);

    void delete(Long id,String updateBy);

    List<ReportComponentDO> queryByIdListAndDataSetId(List<Long> dataScreenReportCardIdList, Long datasetId);

    List<AxisAndChartFieldDO> queryByChartTypeList(List<Integer> chartTypeList);

    void updateCoordinateAxisConfigById(Long reportId, String coordinateAxisConfigRequest);
}
