package com.bestpay.bigdata.bi.database.api.impl.datascreen;


import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.database.api.datascreen.ImageComponentDaoService;
import com.bestpay.bigdata.bi.database.dao.datascreen.ImageComponentDO;
import com.bestpay.bigdata.bi.database.mapper.datascreen.ImageComponentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-08-28
 */
@Component
@Slf4j
public class ImageComponentDaoServiceImpl implements ImageComponentDaoService {

    @Resource
    private ImageComponentMapper imageComponentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(ImageComponentDO imageComponentDO){
        imageComponentMapper.insert(imageComponentDO);
        return imageComponentDO.getId();
    }


    @Override
    public void delete(Long cardId,String updateBy){
        ImageComponentDO delete = ImageComponentDO.builder()
            .id(cardId)
            .statusCode(StatusCodeEnum.DELETE.getCode())
            .updatedAt(new Date())
            .updatedBy(updateBy)
            .build();
        imageComponentMapper.update(delete);
    }

    @Override
    public ImageComponentDO findById(Long id) {
        return imageComponentMapper.findById(id);
    }
}
