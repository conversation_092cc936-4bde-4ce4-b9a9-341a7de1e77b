package com.bestpay.bigdata.bi.database.mapper.warn;

import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnRuleDTO;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName: ReportWarnRuleMapper
 * Package: com.bestpay.bigdata.bi.database.mapper
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/4 14:50
 * @Version 1.0
 */
public interface ReportWarnRuleMapper {

    int batchInsert(List<ReportWarnRuleDo> list);

    int batchShadeInsert(List<ReportWarnRuleDo> list);

    List<ReportWarnRuleDo> queryShade(ReportWarnRuleDTO ruleDo);

    int update(ReportWarnRuleDo ruleDo);

    int updateById(ReportWarnRuleDo ruleDo);

    List<ReportWarnRuleDo> query(ReportWarnRuleDTO ruleDo);

    List<ReportWarnRuleDo> findOriginal(ReportWarnRuleDTO ruleDo);

    List<ReportWarnRuleDo> findBak();

    void batchInsertBak(@Param("list") List<ReportWarnRuleDo> reportWarnRuleDos);
}
