package com.bestpay.bigdata.bi.database.mapper.dashboard;

import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashboardQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @author: laiyao
 * @date: 2022/07/26
 */
public interface DashboardMapper {

    List<Dashboard> queryByCondition(DashboardQuery dashboardQuery);

    int insert(Dashboard dashboard);

    Dashboard getById(Long id);

    int updateById(Dashboard dashboard);

    List<Dashboard> queryListByDirectoryId(@Param("directoryId") Long directoryId);

    Long getMaxDirSort(@Param("displayType") Integer displayType, @Param("directoryId") Long directoryId);

    List<Dashboard> queryByIdList(@Param("dashboardIdList") List<Long> dashboardIdList);

    List<Long> queryAllIdList();
}
