package com.bestpay.bigdata.bi.database.dao.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ClassName: ReportDirectoryDo
 * Package: com.bestpay.bigdata.bi.database.dao.report
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/12/18 11:01
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportDirectoryDo
{

    private static final long serialVersionUID = -19310982607151510L;

    private Long id;

    private Long parentId;

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 目录名称
     */
    private String name;

    /**
     * 类型
     */
    private Integer type;


    /**
     * 目录顺序
     */
    private Long dirSort;


    /**
     * 状态（0：正常   9：删除）
     */
    private Integer statusCode;

    private Date createdAt;

    private String createdBy;

    private Date updatedAt;

    private String updatedBy;
}
