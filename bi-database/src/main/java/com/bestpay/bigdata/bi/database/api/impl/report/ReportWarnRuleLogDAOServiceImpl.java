package com.bestpay.bigdata.bi.database.api.impl.report;


import com.bestpay.bigdata.bi.database.api.report.ReportWarnRuleLogDAOService;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleLogDo;
import com.bestpay.bigdata.bi.database.mapper.warn.ReportWarnRuleLogMapper;
import java.io.Serializable;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * ClassName: ReportWarnRuleLogDAOServiceImpl
 * Package: com.bestpay.bigdata.bi.database.api.impl
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/4 15:48
 * @Version 1.0
 */
@Component
public class ReportWarnRuleLogDAOServiceImpl implements ReportWarnRuleLogDAOService, Serializable {

    @Resource
    private ReportWarnRuleLogMapper reportWarnRuleLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(ReportWarnRuleLogDo reportWarnRuleLogDo) {
        reportWarnRuleLogMapper.insert(reportWarnRuleLogDo);
        return reportWarnRuleLogDo.getId();
    }
}
