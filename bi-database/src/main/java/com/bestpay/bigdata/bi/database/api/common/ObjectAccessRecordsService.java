package com.bestpay.bigdata.bi.database.api.common;

import com.bestpay.bigdata.bi.database.dao.common.ObjectAccessRecordsDO;
import java.util.List;
import java.util.Map;

/**
 * 统计函数表(TFuncInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-11 10:26:36
 */
public interface ObjectAccessRecordsService{
  void insert(ObjectAccessRecordsDO recordsDO);

  List<Map<Long, Long>> countByObjectId(String objectType, List<Long> idList,String userSource);
}
