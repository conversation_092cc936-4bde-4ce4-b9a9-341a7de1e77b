package com.bestpay.bigdata.bi.database.dao.dataset;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-07-21-15:47
 */
@Data
public class DatasetFileUploadFieldDo implements Serializable {

    private Long id;

    private Long tableId;

    /**字段英文名*/
    private String fieldEnName;

    /**字段中文名*/
    private String fieldCnName;

    /**字段原始类型*/
    private String fieldOriginType;

    // 描述
    private String fieldComment;

    /**字段展示类型*/
    private String fieldType;

    /**记录状态*/
    private Integer statusCode;

    private Date createdAt;

    private String createdBy;

    private Date updatedAt;

    private String updatedBy;
}
