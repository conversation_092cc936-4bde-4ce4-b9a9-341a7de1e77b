package com.bestpay.bigdata.bi.database.api.subscribe;

import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDTO;
import com.bestpay.bigdata.bi.database.bean.subscribe.SubDTO;

import java.util.List;

/**
 * @author: den<PERSON><PERSON><PERSON>
 * @date: 2022/12/20
 */
public interface ObjectSubscribeService {


    Long insert(ObjectSubScribeDO dashboardSubScribeDO);

    ObjectSubScribeDO getById(Long id);

    ObjectSubScribeDO getByCode(String code);

    int updateById(ObjectSubScribeDO dashboardSubScribeDO);

    int updateByDashboardId(ObjectSubScribeDO dashboardSubScribeDO);

    List<ObjectSubScribeDO> queryAll();

    List<ObjectSubScribeDO> queryByTaskName(String taskName, Long id);

    List<ObjectSubScribeDO> queryByObjSubDTO(ObjectSubScribeDTO objectSubscribeDTO);

    /**
     * 查找最大的订阅code
     * @return 最大的code
     */
    String queryMaxSubCode();
    /**
     * 根据订阅对象查询订阅任务列表
     *
     * @param objectSubscribeDTO 订阅对象 DTO
     * @return 订阅任务列表
     */
    List<SubDTO> getSubscriptionTaskList(ObjectSubScribeDTO objectSubscribeDTO);

    /**
     * 根据订阅类型查询非9的订阅任务列表
     *
     * @param subType 订阅类型
     * @return 订阅任务列表
     */
    List<ObjectSubScribeDO> queryBySubType(String subType);

    List<ObjectSubScribeDO> findOriginal(ObjectSubScribeDTO objectSubscribeDTO);

    List<ObjectSubScribeDO> findBak();

    void batchInsert(List<ObjectSubScribeDO> subscribeList);
}
