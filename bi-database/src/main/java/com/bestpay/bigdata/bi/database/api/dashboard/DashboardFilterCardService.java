package com.bestpay.bigdata.bi.database.api.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.FilterCardQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DashboardFilterCardService {
    Long insert(DashboardFilterCardDO filterCardDO);

    DashboardFilterCardDO findById(Long id);

    List<DashboardFilterCardDO> find(FilterCardQueryDTO cardQueryDTO);

    void update(Long dashboardId, int statusCode, String updateBy);

    void updateAll(DashboardFilterCardDO filterCardDO);

    List<DashboardFilterCardDO> findOriginal();

    List<DashboardFilterCardDO> findBak();

    void batchInsert(List<DashboardFilterCardDO> filterCardList);
}
