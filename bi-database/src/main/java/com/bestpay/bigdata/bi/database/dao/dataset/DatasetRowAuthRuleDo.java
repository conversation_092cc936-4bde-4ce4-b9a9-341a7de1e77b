package com.bestpay.bigdata.bi.database.dao.dataset;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DatasetRowAuthRuleDo {
    private Long id;
    private Long datasetId;
    private String ruleName;
    private String ruleType;
    private String userType;
    private String authUsers;
    private String value;
    private Integer statusCode;
    private Date createdAt;
    private String createdBy;
    private Date updatedAt;
    private String updatedBy;
}
