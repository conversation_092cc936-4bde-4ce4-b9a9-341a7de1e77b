package com.bestpay.bigdata.bi.database.api.impl.dataset;

import com.bestpay.bigdata.bi.database.api.dataset.DatasetCorrectionLogService;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetCorrectionLogDO;
import com.bestpay.bigdata.bi.database.mapper.dataset.DatasetCorrectionLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据集订正日志服务实现类
 *
 */
@Slf4j
@Service
public class DatasetCorrectionLogServiceImpl implements DatasetCorrectionLogService {

    @Resource
    private DatasetCorrectionLogMapper correctionLogMapper;

    @Override
    public int insert(DatasetCorrectionLogDO correctionLog) {
        try {
            return correctionLogMapper.insert(correctionLog);
        } catch (Exception e) {
            log.error("插入数据订正日志失败", e);
            return 0;
        }
    }

    @Override
    public int batchInsert(List<DatasetCorrectionLogDO> correctionLogs) {
        if (correctionLogs == null || correctionLogs.isEmpty()) {
            return 0;
        }
        
        try {
            return correctionLogMapper.batchInsert(correctionLogs);
        } catch (Exception e) {
            log.error("批量插入数据订正日志失败", e);
            return 0;
        }
    }

    @Override
    public List<DatasetCorrectionLogDO> queryByBatchId(String batchId) {
        try {
            return correctionLogMapper.queryByBatchId(batchId);
        } catch (Exception e) {
            log.error("根据批次ID查询订正日志失败: {}", batchId, e);
            return null;
        }
    }

    @Override
    public List<DatasetCorrectionLogDO> queryByRuleId(Long ruleId) {
        try {
            return correctionLogMapper.queryByRuleId(ruleId);
        } catch (Exception e) {
            log.error("根据规则ID查询订正日志失败: {}", ruleId, e);
            return null;
        }
    }

    @Override
    public List<DatasetCorrectionLogDO> queryByBatchIdAndStatus(String batchId, String status) {
        try {
            return correctionLogMapper.queryByBatchIdAndStatus(batchId, status);
        } catch (Exception e) {
            log.error("根据批次ID和状态查询订正日志失败: batchId={}, status={}", batchId, status, e);
            return null;
        }
    }
}
