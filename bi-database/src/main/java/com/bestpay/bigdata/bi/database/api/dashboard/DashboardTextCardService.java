package com.bestpay.bigdata.bi.database.api.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.TextCardQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardTextCardDO;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DashboardTextCardService {
    Long insert(DashboardTextCardDO textCardDO);

    DashboardTextCardDO findById(Long id);
    List<DashboardTextCardDO> find(TextCardQueryDTO textCardQueryDTO);
    void update(Long dashboardId,int statusCode, String updateAt);

    List<DashboardTextCardDO> findByIdsAndDataSetId(List<Long> idList);

}
