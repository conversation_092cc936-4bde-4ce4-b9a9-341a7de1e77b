package com.bestpay.bigdata.bi.database.mapper.dataset;


import com.bestpay.bigdata.bi.common.dto.dataset.DatasetQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.ResourceBaseDO;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetDo;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetWithResAccessStatsDo;
import com.bestpay.bigdata.bi.database.dao.dataset.ResAccessStatsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-04-18:22
 */
public interface DatasetMapper {

    List<DatasetDo> query(DatasetQueryDTO queryDTO);

    List<DatasetWithResAccessStatsDo> queryWithAccessStats(DatasetQueryDTO queryDTO);

    DatasetDo findLatestDatasetDo();

    DatasetDo queryDoByDatasetCode(@Param("datasetCode") String datasetCode);

    int updateDatasetDo(DatasetDo datasetDo);

    int insertOrUpdate(DatasetDo datasetDo);

    Long count(DatasetQueryDTO pageQueryDTO);

    List<ResourceBaseDO> queryResList(@Param("statsDO") ResAccessStatsDO statsDO, @Param("screenIds") List<Long> screenIds);

    List<String> resNameList(@Param("datasetId") Long datasetId);

    /**
     * 查询数据集分页信息
     * @param datasetQueryDTO 查询条件 包含分页数据 ，关键字等
     * @return 数据集相关的信息
     */
    List<DatasetDo> queryDataSetList(@Param("datasetQueryDTO") DatasetQueryDTO datasetQueryDTO);

}
