package com.bestpay.bigdata.bi.database.dao.dashboard;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * ClassName: DashboardReportCardDO
 * Package: com.bestpay.bigdata.bi.database.dao.dashboard
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/1/10 13:38
 * @Version 1.0
 */
@Data
public class DashboardReportCardDO
{
    private Long id;
    private Long dashboardId;// 仪表板id

    @ApiModelProperty(value = "选择组织编码",required = true)
    @NotNull(message = "选择组织编码不能为空！")
    private String orgSelected;

    @ApiModelProperty(value = "报表名称",required = true)
    @NotNull(message = "报表名称不能为空！")
    private String reportName;

    @ApiModelProperty(value = "组织权限",required = true)
    private String orgAuth;

    @ApiModelProperty(value = "报表说明",required = true)
    @NotNull(message = "报表说明不能为空！")
    private String reportDesc;

    @ApiModelProperty(value = "数据权限",required = true)
    private String dataAuth;

    @ApiModelProperty(value = "展示字段/维度",required = true)
    private String showColumn;

    @ApiModelProperty(value = "指标控件",required = true)
    private String indexColumn;

    @ApiModelProperty(value = "过滤控件",required = true)
    private String filterColumn;

    @ApiModelProperty(value = "查询控件",required = true)
    private String condition;

    @ApiModelProperty(value = "关键字",required = true)
    private String keyword;

    @ApiModelProperty(value = "排序控件")
    private String orderColumn;

    @ApiModelProperty(value = "计算字段")
    private String computeColumn;

    @ApiModelProperty(value = "报表结构, 描述顺序信息 JSON List ReportSimpleColumn")
    private String reportStructure;

    private String tableConfiguration;

    @ApiModelProperty(value = "对比字段")
    private String contrastColumn;

    @ApiModelProperty(value = "数据源、数据集、表",required = true)
    private String datasetInfo;

    @ApiModelProperty(value = "报表类型（0：明细表  1：聚合表 ）",required = true)
    @NotNull(message = "报表类型不能为空！")
    private Integer reportType;

    @ApiModelProperty(value = "数据是否包含敏感信息: 0: false 1:true",required = true)
    private Integer fileContainSensitiveInfo;

    @ApiModelProperty(value = "敏感字段")
    private String sensitiveFields;

    @ApiModelProperty(value = "状态（0：上线  1：待发布  9：删除）",required = true)
//  @NotNull(message = "状态不能为空！")
    private Integer statusCode;

    @ApiModelProperty(value = "是否开启上卷下钻 0关闭 1开启, 默认值为0")
    private Integer rollupDown;


    @ApiModelProperty(value = "图表类型；0-列表；1-柱形图；2-折线图；3-环形图；",required = true)
    private Integer chartType;

    @ApiModelProperty(value = "图表属性",required = true)
    private String chartField;

    @ApiModelProperty(value = "是否显示指标总计")
    private String showIndexTotal;

    @ApiModelProperty(value = "查询总行数")
    private String maxRows;

    private Date createdAt;

    private String createdBy;

    private Date updatedAt;

    private String updatedBy;


    @ApiModelProperty(value = "表格字体样式")
    private String tableFontStyle;

    @ApiModelProperty(value = "坐标轴配置")
    private String coordinateAxisConfig;

    private String cardStyleConfig;

    /**
     * 图形展示信息
     */
    private String graphicDisplay;


    /**
     * 数据集ID
     */
    private Long datasetId;

    /**
     *  特殊排序 默认为0常规排序 1：堆积柱的指标总和升序排列，2: 堆积柱的指标总和降序排列
     */
    private Integer orderType;
}
