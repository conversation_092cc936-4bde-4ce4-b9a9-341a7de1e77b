package com.bestpay.bigdata.bi.database.api.dataset;

import com.bestpay.bigdata.bi.database.bean.dataset.DatasetFileUploadTableQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetFileUploadTableDo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-21-16:13
 */
public interface DatasetFileUploadTableDAOService {

    List<DatasetFileUploadTableDo> queryList(DatasetFileUploadTableQueryDTO queryDTO);

    int update(DatasetFileUploadTableDo tableDo);


    Long insert(DatasetFileUploadTableDo tableDo);
}
