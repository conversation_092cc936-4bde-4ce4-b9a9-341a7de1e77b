package com.bestpay.bigdata.bi.database.bean.report;

import com.bestpay.bigdata.bi.common.bean.PageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 报表查询请求类
 *
 * <AUTHOR>
 * @since 2022-02-16 11:06:32
 */
@Data
@ApiModel("报表查询请求类")
public class ReportQueryRequest extends PageVo{

  /**
   * 归属组织编码
   */
  @ApiModelProperty(value = "归属组织编码",required = true)
  private String orgCode;
  /**
   * 归属ID
   */
  @ApiModelProperty(value = "归属ID",required = true)
  private Long dirId;

  /**
   * 关键字
   */
  @ApiModelProperty(value = "关键字",required = true)
  private String keyword;
  /**
   * 责任人登录名
   */
  @ApiModelProperty(value = "责任人登录名",required = true)
  private String ownerName;

  /**
   * 状态（0：上线  1：待发布  -1：不限）
   */
  @ApiModelProperty(value = "状态（0：上线  1：待发布  -1：不限）",required = true)
  private Integer statusCode;

  /**
   * email
   */
  @ApiModelProperty(value = "邮箱",required = true)
  private String email;
}
