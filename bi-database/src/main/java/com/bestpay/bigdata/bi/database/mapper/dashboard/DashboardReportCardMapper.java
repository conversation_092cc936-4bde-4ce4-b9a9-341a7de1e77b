package com.bestpay.bigdata.bi.database.mapper.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.ReportCardQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName: DashboardReportCardMapper
 * Package: com.bestpay.bigdata.bi.database.mapper
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/1/10 13:43
 * @Version 1.0
 */
public interface DashboardReportCardMapper
{
    Long insert(DashboardReportCardDO cardDO);

    List<DashboardReportCardDO> find(ReportCardQueryDTO cardQuery);

    Long update(DashboardReportCardDO cardDO);

    List<DashboardReportCardDO> queryAll();

    void updateTableFontStyleAndTableConfigurationById(@Param("dashboardReportCardDO") DashboardReportCardDO dashboardReportCardDO);

    List<DashboardReportCardDO> queryByChartTypes(@Param("chartTypeList") List<Integer> chartTypeList);

    // 新增备份表操作方法
    List<DashboardReportCardDO> findOriginal();

    List<DashboardReportCardDO> findBak();

    void batchInsert(@Param("reportCardList") List<DashboardReportCardDO> reportCardList);
}
