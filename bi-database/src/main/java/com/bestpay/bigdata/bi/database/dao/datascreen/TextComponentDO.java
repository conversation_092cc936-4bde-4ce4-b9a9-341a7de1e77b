package com.bestpay.bigdata.bi.database.dao.datascreen;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextComponentDO {
  private static final long serialVersionUID = -88089501988327986L;

  /**
   * 组件 ID
   */
  private Long id;

  /**
   * 文本组件配置信息
   */
  private String componentConfig;

  /**
   * 状态（0：已上线  1：已下线  9：删除）
   */
  private Integer statusCode;

  /**
   * 创建时间
   */
  private Date createdAt;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新时间
   */
  private Date updatedAt;

  /**
   * 更新人
   */
  private String updatedBy;
}
