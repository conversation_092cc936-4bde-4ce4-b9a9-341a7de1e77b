package com.bestpay.bigdata.bi.database.api.impl;

import com.bestpay.bigdata.bi.database.api.common.RegressionCheckFailLogService;
import com.bestpay.bigdata.bi.database.dao.common.RegressionCheckFailLogDO;
import com.bestpay.bigdata.bi.database.mapper.common.RegressionCheckFailLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class RegressionCheckFailLogServiceImpl implements RegressionCheckFailLogService {

    @Resource
    private RegressionCheckFailLogMapper regressionCheckFailLogMapper;

    @Override
    public void batchInsert(List<RegressionCheckFailLogDO> regressionCheckFailLogDOS) {
        regressionCheckFailLogMapper.batchInsert(regressionCheckFailLogDOS);
    }
}
