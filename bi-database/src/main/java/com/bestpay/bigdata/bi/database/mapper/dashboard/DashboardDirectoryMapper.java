package com.bestpay.bigdata.bi.database.mapper.dashboard;

import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardDirectoryDo;
import java.util.List;

import com.bestpay.bigdata.bi.database.dao.common.DirectoryTreeDO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @create 2023-06-02-10:21
 */
public interface DashboardDirectoryMapper {

    long insertDb(DashboardDirectoryDo directoryDo);

    int updateDbById(DashboardDirectoryDo directoryDo);

    int updateDbByIdForMove(DashboardDirectoryDo directoryDo);

    List<DashboardDirectoryDo> getDirectoryListByName(@Param("name") String name);

    DashboardDirectoryDo queryDbById(Long id);

    List<DashboardDirectoryDo> getDashboardQueryDirectoryList(@Param("orgAuth") String userOrgCode);

    Long getMaxDirSort(@Param("type") Integer type);

    List<DashboardDirectoryDo> getDashboardDirectoryList(@Param("orgCode") String orgCode);

    List<DashboardDirectoryDo> queryDashboardDirectoryList(DashboardDirectoryDo directoryDo);

    List<DirectoryTreeDO> queryDashboardDirectoryTreeListByOrgCode(@Param("orgCode") String orgCode);

    List<DirectoryTreeDO> queryDirTreeByIdList(@Param("dirIdList") List<Long> dirIdList);

    List<DirectoryTreeDO> queryDashboardDirectoryTreeListByOrgCodeSub(Integer parentId);
}
