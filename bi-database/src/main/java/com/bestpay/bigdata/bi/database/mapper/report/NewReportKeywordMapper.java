package com.bestpay.bigdata.bi.database.mapper.report;

import com.bestpay.bigdata.bi.database.dao.report.component.NewReportKeywordDO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface NewReportKeywordMapper {

  List<NewReportKeywordDO> getById(Long reportId);

  Long insertShade(List<NewReportKeywordDO> computeDO);

  List<NewReportKeywordDO> findByShadeReportId(@Param("reportId")
                                          Long reportId,@Param("resourceType") String resourcetype,@Param("componentType") String componenttype);

  Long insert(List<NewReportKeywordDO> computeDO);

  int update(NewReportKeywordDO computeDO);

  List<NewReportKeywordDO> findAll(@Param("resourceType") String resourcetype,@Param("componentType") String componenttype);

    List<NewReportKeywordDO> findAllShade(@Param("resourceType") String resourcetype,@Param("componentType") String componenttype);

    List<NewReportKeywordDO> findByReportId(@Param("reportId")
      Long reportId,@Param("resourceType") String resourcetype,@Param("componentType") String componenttype);
}
