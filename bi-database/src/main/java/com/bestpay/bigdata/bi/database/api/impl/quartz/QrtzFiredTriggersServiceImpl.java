package com.bestpay.bigdata.bi.database.api.impl.quartz;

import com.bestpay.bigdata.bi.database.api.quartz.QrtzFiredTriggersService;
import com.bestpay.bigdata.bi.database.mapper.quartz.QrtzFiredTriggersMapper;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * qrtz_fired_triggers 服务实现类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class QrtzFiredTriggersServiceImpl implements QrtzFiredTriggersService {

  @Resource
  private QrtzFiredTriggersMapper qrtzFiredTriggersMapper;

  @Override
  public int selectCountByState(String state, String triggerName) {
    return qrtzFiredTriggersMapper.selectCountByState(state, triggerName);
  }


}
