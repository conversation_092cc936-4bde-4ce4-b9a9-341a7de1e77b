package com.bestpay.bigdata.bi.database.api.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.IndexCardQueryDTO;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DashboardIndexTextCardService {
    Long insert(DashboardIndexTextCardDO cardDO);

    List<DashboardIndexTextCardDO> find(IndexCardQueryDTO cardQuery);

    Long update(DashboardIndexTextCardDO cardDO);

    void delete(Long dashboardId,String updateBy);

    List<DashboardIndexTextCardDO> queryByDashboardIdList(List<Long> dashboardIdList, String indexCardType);

    void batchInsert(List<DashboardIndexTextCardDO> dashboardIndexTextCardDOList);

    List<DashboardIndexTextCardDO> findOriginal();

    List<DashboardIndexTextCardDO> findBak();

    void batchInsertBak(List<DashboardIndexTextCardDO> indexTextCardList);

}
