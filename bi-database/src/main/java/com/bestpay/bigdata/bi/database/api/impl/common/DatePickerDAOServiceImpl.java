package com.bestpay.bigdata.bi.database.api.impl.common;


import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.database.mapper.common.DatePickerConfigMapper;
import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应用嵌入(AppEmbed)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-14 13:58:25
 */
@Component
public class DatePickerDAOServiceImpl implements DatePickerDAOService {

    @Resource
    private DatePickerConfigMapper pickerConfigMapper;

    @Override
    public DatePickerConfigDO select(Long id){
        return pickerConfigMapper.select(Lists.newArrayList(id)).get(0);
    }

    @Override
    public List<DatePickerConfigDO> select(List<Long> ids){
        return pickerConfigMapper.select(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(DatePickerConfigDO picker){
        picker.setUpdatedBy(UserContextUtil.get().getEmail());
        picker.setCreatedBy(UserContextUtil.get().getEmail());
        picker.setUpdatedAt(new Date());
        picker.setCreatedAt(new Date());

        pickerConfigMapper.insert(picker);
        return picker.getId();
    }

}
