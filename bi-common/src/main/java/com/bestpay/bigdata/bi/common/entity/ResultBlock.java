package com.bestpay.bigdata.bi.common.entity;

import com.google.common.collect.ImmutableList;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2023-02-13-17:42
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ResultBlock implements Serializable {
    private ResultBlockMetadata metadata;
    private List<List<String>> data;

    public void setData(List<List<String>> data) {
        this.data = ImmutableList.copyOf(data);
    }
}
