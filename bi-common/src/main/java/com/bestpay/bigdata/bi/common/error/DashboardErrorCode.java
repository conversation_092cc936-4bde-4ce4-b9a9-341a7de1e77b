package com.bestpay.bigdata.bi.common.error;

public enum DashboardErrorCode implements ErrorCodeSupplier {

    DASHBOARD_ONLY_MANAGER_OPERATE("00001", ErrorType.BUSINESS_ERROR),
    DASHBOARD_CARD_TYPE_NOT_EXIST("00002", ErrorType.BUSINESS_ERROR),
    DASHBOARD_INDEX_TEXT_NOT_EXIST("00003", ErrorType.BUSINESS_ERROR),
    DASHBOARD_DEL_FIELD_TIP("00004", ErrorType.BUSINESS_ERROR),
    DASHBOARD_MODIFY_FIELD_TIP("00005", ErrorType.BUSINESS_ERROR),
    DASHBOARD_FIELD_TYPE_TIP("00006", ErrorType.BUSINESS_ERROR),
    DASHBOARD_STORE_CARD_LIST_ERROR("00007", ErrorType.BUSINESS_ERROR),
    DASHBOARD_REDIS_DATA_NOT_EXIST("00008", ErrorType.BUSINESS_ERROR),
    DASHBOARD_TABLEAU_DOWNLOAD_FAIL("00009", ErrorType.BUSINESS_ERROR),
    DASHBOARD_DASHBOARD_ID_NULL("00010", ErrorType.BUSINESS_ERROR),
    DASHBOARD_DASHBOARD_CARD_TYPE_NULL("00011", ErrorType.BUSINESS_ERROR),
    DASHBOARD_DASHBOARD_CARD_INFO_NULL("00012", ErrorType.BUSINESS_ERROR),
    DASHBOARD_CARD_CODE_INFO_NULL("00013", ErrorType.BUSINESS_ERROR),
    DASHBOARD_ATTR_TYPE_NULL("00014", ErrorType.BUSINESS_ERROR),
    DASHBOARD_CONF_NULL("00015", ErrorType.BUSINESS_ERROR),
    DASHBOARD_CARD_CODE_LIST_INFO_NULL("00016", ErrorType.BUSINESS_ERROR),
    DASHBOARD_CONF_ERROR("00017", ErrorType.BUSINESS_ERROR),
    DASHBOARD_NOT_PUBLISH_MOBILE("00018", ErrorType.BUSINESS_ERROR),
    DASHBOARD_CHART_NOT_SUPPORT_SCENE_ERROR("00019", ErrorType.BUSINESS_ERROR),
    DASHBOARD_CHART_TYPE_ERROR("00020", ErrorType.BUSINESS_ERROR),

    DASHBOARD_NAME_EXISTS("10001", ErrorType.USER_ERROR),
    DASHBOARD_NOT_SUPPORT_CARD_TYPE("10002", ErrorType.USER_ERROR),
    DASHBOARD_DIRECTORY_SAME_NAME_ERROR("10003", ErrorType.USER_ERROR),
    DASHBOARD_DIRECTORY_NOT_EMPTY_ERROR("10004", ErrorType.BUSINESS_ERROR),
    DASHBOARD_NOT_EXISTS("10005", ErrorType.USER_ERROR),
    DASHBOARD_DATA_EMPTY("10006", ErrorType.USER_ERROR),
    DASHBOARD_FILTER_MUST_HAVE_DEFAULT("10007", ErrorType.USER_ERROR),
    DASHBOARD_MUST_CONF_DIMENSION_OR_MEASURE("10008", ErrorType.USER_ERROR),
    DASHBOARD_HIDDEN_TIP("10009", ErrorType.USER_ERROR),
    DASHBOARD_CONF_INFO_CHANGED("10005", ErrorType.BUSINESS_ERROR),



    ;

    private static final String PREFIX = "DASHBOARD_";

    private final ErrorCode errorCode;

    DashboardErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
