package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author：Song
 * @Date：2024/11/4 14:36
 * @Desc:  图形展示
 */
@Data
@ApiModel(description = "图形展示")
public class GraphicDisplayDTO {

    /**
     * 线条类型   1 直线   2 曲线
     */
    @ApiModelProperty(value = "线条类型   1 直线   2 曲线")
    private Integer lineType;

    /**
     * 线条样式   1连续线  2小断点线 3大断点线
     */
    @ApiModelProperty(value = "线条样式   1连续线  2小断点线 3大断点线")
    private String lineStyle;

    /**
     * 线条大小  2-10以内的正整数
     */
    @ApiModelProperty(value = "线条大小  2-10以内的正整数")
    private Integer lineSize;

    /**
     * 是否展示标记点
     */
    @ApiModelProperty(value = "是否展示标记点")
    private Boolean showMarkPoint;

    /**
     * 标记点大小
     */
    @ApiModelProperty(value = "标记点大小")
    private Integer markPointSize;



    public static void main(String[] args) {
        GraphicDisplayDTO request = new GraphicDisplayDTO();
        request.setLineType(1);
        request.setLineStyle("dashed");
        request.setLineSize(10);
        request.setShowMarkPoint(false);
        request.setMarkPointSize(30);
        System.out.println(JSONUtil.toJsonStr(request).length());
    }





}
