package com.bestpay.bigdata.bi.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/20 14:20
 * @Description :
 **/
@ApiModel(value = "父类")
@NoArgsConstructor
@Data
public class FatherHeaderInfo {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "uuid")
    private String uuid;
    @ApiModelProperty(value = "标签")
    private String label;
    @ApiModelProperty(value = "报表字段")
    private String reportField;
    @ApiModelProperty(value = "聚合")
    private String polymerization;
    @ApiModelProperty(value = "英文名")
    private String enName;
}
