package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 过滤器卡片
 * <AUTHOR>
 */
@Data
@ApiModel("过滤器卡片")
public class NewFilterCardDTO extends NewCardDTO {
    @ApiModelProperty(value = "筛选器类型")
    private String filterDateType;
    @ApiModelProperty(value = "筛选器配置信息， 名称等")
    private Fields fields;
    @ApiModelProperty(value = "关联卡片信息")
    private List<RelateCardInfo> relateCardsInfo;
    @ApiModelProperty(value = "数据集ID")
    private Long datasetId;

    @Data
    @ApiModel("字段信息")
    public static class Fields{
        @ApiModelProperty(value = "日期类型")
        private String dateType;
        @ApiModelProperty(value = "默认类型")
        private String defaultType;
        @ApiModelProperty(value = "参数 默认值")
        private Object defaultValue;
        @ApiModelProperty(value = "参数 默认值")
        private List<String> defaultValues;
        @ApiModelProperty(value = "字段英文名称")
        private String enName;
        @ApiModelProperty(value = "过滤类型")
        private String filterType;
        @ApiModelProperty(value = "字段ID")
        private Long id;
        @ApiModelProperty(value = "唯一标识")
        private String uuid;
        @ApiModelProperty(value = "报表配置唯一标识")
        private String configUuid;
        @ApiModelProperty(value = "是否包含当前")
        private Boolean includeCurrent;
        @ApiModelProperty(value = "字段名称")
        private String name;
        @ApiModelProperty(value = "报表字段")
        private String reportField;
        @ApiModelProperty(value = "字段显示类型名称")
        private String showTypeName;
        @ApiModelProperty(value = "字段值")
        private String stringValue;
        @ApiModelProperty(value = "时间类型")
        private String timeType;
        @ApiModelProperty(value = "类型名称")
        private String typeName;
        @ApiModelProperty(value = "字段值")
        private List<String> value;
        @ApiModelProperty(value = "字段值")
        private List<String> valueList;
        @ApiModelProperty(value = "排序")
        private String sort;
    }

    @Data
    @ApiModel("关联卡片信息")
    public static class RelateCardInfo{
        @ApiModelProperty(value = "卡片ID")
        private String cardId;
        @ApiModelProperty(value = "卡片类型")
        private String cardType;
        @ApiModelProperty(value = "字段名称")
        private String fieldName;
        @ApiModelProperty(value = "字段类型")
        private String fieldType;
        @ApiModelProperty(value = "显示类型名称")
        private String showTypeName;
        @ApiModelProperty(value = "字段值")
        private String value;
    }
}
