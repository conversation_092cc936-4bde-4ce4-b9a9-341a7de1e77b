package com.bestpay.bigdata.bi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

public enum JavaFieldTypeEnum {
    STRING("String"),
    BYTE("Byte"),
    SHORT("Short"),
    INT("Int"),
    LONG("Long"),
    FLOAT("Float"),
    DOUBLE("Double"),
    TIMESTAMP("Timestamp"),
    DATE("Date"),
    DATETIME("DataTime"),
    B<PERSON><PERSON>CIMAL("bigDecimal"),;

    @Getter
    private final String code;

    JavaFieldTypeEnum(String code) {
        this.code = code;
    }

    public static JavaFieldTypeEnum getByCode(String code) {
        for (JavaFieldTypeEnum cardType : values()) {
            if (cardType.code.equals(code)) {
                return cardType;
            }
        }

        return null; // Code not found
    }
}
