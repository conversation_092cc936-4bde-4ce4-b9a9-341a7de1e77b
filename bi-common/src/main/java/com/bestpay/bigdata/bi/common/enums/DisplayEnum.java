package com.bestpay.bigdata.bi.common.enums;

import static com.bestpay.bigdata.bi.common.enums.CodeEnum.ENUM_CODE_ERROR;

import com.bestpay.bigdata.bi.common.exception.BusinessException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

/**
 * @Author: dengyanwei
 * @CreateDate: 2021/12/17 14:52
 */
public enum DisplayEnum {

  /**
   * 柱形图
   */
  BAR_DISPLAY(0),
  /**
   * 列表
   */
  List_DISPLAY(1);

  @Getter
  private final int code;

  private static final Map<Integer, DisplayEnum> ENUM_VALUE_MAP;

  DisplayEnum(int code) {
    this.code = code;
  }

  static {
    //结束状态
    Map<Integer, DisplayEnum> valueMap = new HashMap<>();
    DisplayEnum[] displayEnums = DisplayEnum.values();

    for (DisplayEnum displayEnum : displayEnums) {
      valueMap.put(displayEnum.code, displayEnum);
    }
    ENUM_VALUE_MAP = Collections.unmodifiableMap(valueMap);
  }

  public static DisplayEnum getStatus(int code) {
    DisplayEnum displayEnum = ENUM_VALUE_MAP.get(code);
    if (ObjectUtils.isEmpty(displayEnum)) {
      throw new BusinessException(ENUM_CODE_ERROR);
    }
    return displayEnum;
  }

}
