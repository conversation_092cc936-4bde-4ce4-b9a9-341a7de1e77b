package com.bestpay.bigdata.bi.common.response;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/9/8
 */
public class QueryResult implements Serializable {

  private List<String> columnNames;

  private List<List<String>> data;

  private Double queryTimeInSecond;

  private Integer total;

  public List<String> getColumnNames() {
    return columnNames;
  }

  public void setColumnNames(List<String> columnNames) {
    this.columnNames = columnNames;
  }

  public List<List<String>> getData() {
    return data;
  }

  public void setData(List<List<String>> data) {
    this.data = data;
  }

  public Double getQueryTimeInSecond() {
    return queryTimeInSecond;
  }

  public void setQueryTimeInSecond(Double queryTimeInSecond) {
    this.queryTimeInSecond = queryTimeInSecond;
  }

  public Integer getTotal() {
    return total;
  }

  public void setTotal(Integer total) {
    this.total = total;
  }
}
