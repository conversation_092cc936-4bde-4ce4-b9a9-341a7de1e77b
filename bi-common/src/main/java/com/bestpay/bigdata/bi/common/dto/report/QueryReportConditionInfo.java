package com.bestpay.bigdata.bi.common.dto.report;

import com.bestpay.bigdata.bi.common.dto.report.component.CommonComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lcy
 * @date: 2022/2/16
 **/
@Data
@ApiModel("查询报表条件信息")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryReportConditionInfo extends CommonComponentPropertyDTO {

    @ApiModelProperty(value = "范围过滤类型",required = true,allowableValues = ScopeFilterTypeEnum.ALL_VALUE)
    private String scopeFilterType;

    @ApiModelProperty(value = "聚合方式")
    private String polymerization;

    @ApiModelProperty(value = "字段英文名称",required = true)
    private String fieldName;

    @ApiModelProperty(value = "比较符用到的常量值，当fieldType是SELECT-INPUT字符时，这个字段有值，逗号隔开")
    private String stringValue;

    @ApiModelProperty(value = "高级函数")
    private List<HighFunction> highFunctions;

    @ApiModelProperty(value = "区间 1动态时间 2固定时间")
    private String intervalType;

    @ApiModelProperty(value = "如果选择了动态时间,这里为动态时间 见FilterDateValueEnum 定义")
    private String dynamicDate;

    @ApiModelProperty(value = "日期选择器ID")
    private Long datePickerId;


    @ApiModelProperty(value = "日期类型")
    private String dateType;

    @ApiModelProperty(value = "筛选类型")
    private String filterType;


    @ApiModelProperty(value = "时间类型")
    private String timeType;


    @ApiModelProperty(value = "默认类型")
    private String defaultType;


    @ApiModelProperty(value = "默认值")
    private List<String> defaultValues;


    @ApiModelProperty(value ="是否包含本周期" )
    private Boolean includeCurrent;

    @ApiModelProperty(value ="是否需要绝对时间" )
    private Boolean isNoNeedAbsoluteTime;

    @ApiModelProperty(value = "筛选器名称")
    private String cardName;

    @ApiModelProperty(value = "筛选器名称")
    private String cardUuid;
}
