package com.bestpay.bigdata.bi.common.execution;

import com.bestpay.bigdata.bi.common.api.RedisService;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.entity.ResultBlock;
import com.bestpay.bigdata.bi.common.entity.ResultBlockMetadata;
import com.google.common.collect.Lists;
import com.google.common.hash.HashCode;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2023-02-13-17:27
 */
@Component
public class DataManageByRedis implements DataManage {

    public final String RESULT_BLOCK_PREFIX = "RESULT_BLOCK_KEY_PREFIX:%s";
    private static final Integer GAP = 600;

    @Resource
    private RedisService redisService;

    @Resource
    private ApolloRefreshConfig apolloRefreshConfig;

    @Override
    public void writeResultBlock(ResultBlock resultBlock) {
        String redisKey = generateResultBlockKey(resultBlock.getMetadata());
        redisService.setObj(redisKey, resultBlock, getExpiredTime(), TimeUnit.SECONDS);
    }

    @Override
    public ResultBlock readResultBlock(ResultBlockMetadata metadata) {
        String redisKey = generateResultBlockKey(metadata);
        Optional<ResultBlock> resultBlock = redisService.getObj(redisKey);
        return resultBlock.orElse(null);
    }

    @Override
    public void expireResultBlockList(List<ResultBlockMetadata> resultBlockMetadataList) {
        for (ResultBlockMetadata resultBlockMetadata : resultBlockMetadataList) {
            String redisKey = generateResultBlockKey(resultBlockMetadata);
            redisService.del(redisKey);
        }
    }

    private String generateResultBlockKey(ResultBlockMetadata metadata){
        ArrayList<String> args = Lists.newArrayList(metadata.getQueryId(),
                metadata.getNumber() + "");
        HashFunction function = Hashing.sha256();
        Hasher hasher = function.newHasher();
        hasher.putString(args.toString(), StandardCharsets.UTF_8);
        HashCode hashCode = hasher.hash();
        return String.format(RESULT_BLOCK_PREFIX, hashCode.toString());
    }

    private Integer getExpiredTime() {
        return apolloRefreshConfig.getQueryTimeoutSecond()+GAP;
    }
}
