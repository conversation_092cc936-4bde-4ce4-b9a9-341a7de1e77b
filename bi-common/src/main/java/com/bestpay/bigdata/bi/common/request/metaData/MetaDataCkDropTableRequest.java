package com.bestpay.bigdata.bi.common.request.metaData;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2022/6/14 9:38
 */
@Data
public class MetaDataCkDropTableRequest implements Serializable {
  private static final long serialVersionUID = -1545123056929108713L;
  private String sourceName;
  private String dbName;
  private String tableName;
  private String owner;
}
