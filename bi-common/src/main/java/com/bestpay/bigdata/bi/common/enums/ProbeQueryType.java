package com.bestpay.bigdata.bi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR> @Description: 即席查询类型
 * @date 2022/01/17
 */
public enum ProbeQueryType {
    /**
     * 普通查询
     */
    COMMON(0, "probe_common","普通查询"),

    /**
     * 高级查询
     */
    ADVANCED(1, "probe_advanced","高级查询"),
    ;

    @Getter
    private final int code;

    @Getter
    private final String source;

    @Getter
    private final String desc;

    ProbeQueryType(int code, String source, String desc) {
        this.code = code;
        this.source = source;
        this.desc = desc;
    }

    public static ProbeQueryType getEnumBySource(String source){

        for (ProbeQueryType value : ProbeQueryType.values()) {
            if(value.getSource().equalsIgnoreCase(source)){
                return value;
            }
        }

        return null;
    }

    public static ProbeQueryType getEnumByCode(int code){

        for (ProbeQueryType value : ProbeQueryType.values()) {
            if(value.getCode()==code){
                return value;
            }
        }

        return null;
    }
}
