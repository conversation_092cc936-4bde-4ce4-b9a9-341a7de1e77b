package com.bestpay.bigdata.bi.common.dto.report.component;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.report.AdvancedComputing;
import com.bestpay.bigdata.bi.common.dto.report.HighFunction;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.dto.report.function.LodFunctionInfo;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@ApiModel(description = "通用组件属性")
public class CommonComponentPropertyDTO {

  private static final String REPORT_UUID_KEY = "report";

  @ApiModelProperty(value = "维度ID")
  protected Long id;

  @ApiModelProperty(value = "日期类型维度统计类型 1-日；2-月；3-年；4-周；5-季度")
  private Integer dateGroupType;

  @ApiModelProperty(value = "计算逻辑")
  private String calculateLogic;

  @ApiModelProperty(value = "计算字段解析后的逻辑")
  private String computeFieldLogic;

  @ApiModelProperty(value = "是否是计算字段")
  private boolean isComputeField;

  @ApiModelProperty(value = "数据来源(数据集、报表计算字段)唯一来源标识")
  protected String uuid;

  @ApiModelProperty(value = "报表配置的唯一标识")
  protected String configUuid;

  @ApiModelProperty(value = "类型名称")
  private String typeName;

  @ApiModelProperty(value = "源字段英文")
  private String originEnName;

  @ApiModelProperty(value = "中文-字段名称",notes = "入参")
  protected String name;

  @ApiModelProperty(value = "指标名称")
  protected String indexName;

  @ApiModelProperty(value = "字段英文名称")
  protected String enName;

  @ApiModelProperty(value = "中文-字段原名",notes = "入参")
  protected String originalName;

  @ApiModelProperty(value = "字段显示类型名称")
  protected String showTypeName;

  @ApiModelProperty(value = "计算字段计算逻辑")
  protected String fun;

  @ApiModelProperty(value = "是否折叠")
  protected Boolean isFold;

  @ApiModelProperty(value = "类型名称")
  protected String reportField;

  @ApiModelProperty(value = "高级函数列表")
  private List<HighFunction> highFunctions;

  @ApiModelProperty(value = " Lod高级函数信息")
  private LodFunctionInfo functionInfo;

  @ApiModelProperty(value = "字段类型")
  protected String fieldType;

  @ApiModelProperty(value = "字段名称")
  private String fieldName;

  @ApiModelProperty(value = "资源类型")
  private String resourceType;

  @ApiModelProperty(value = "组件类型")
  private String componentType;

  @ApiModelProperty(value = "比较符用到的常量值",required = true)
  protected List<Object> values;

  @ApiModelProperty(value = "目前是 层级的 uuid")
  private String parentId;

  public String getUuid() {
//    if(uuid==null){
//      return REPORT_UUID_KEY + UUID.randomUUID();
//    }

    return uuid;
  }

  private static String getUuid(String originUuid, Map<String, String> computerUuidMap){
    if(originUuid==null){
      throw new BusinessException("配置字段数据存在异常!");
    }
    if (!originUuid.startsWith(ReportUuidGenerateUtil.REPORT_COMPUTE_UUID_PREFIX)) {
      // 对于非报表里面的计算字段，复制时无需生成新的uuid
      computerUuidMap.put(originUuid, originUuid);
      return originUuid;
    }

    String newUuid = computerUuidMap.get(originUuid);
    // 报表里面的计算字段，复制时需要重新生成
    if(newUuid==null){
      newUuid = ReportUuidGenerateUtil.generateReportComputeUuid();
      computerUuidMap.put(originUuid, newUuid);
    }
    return newUuid;
  }


  public static void setUuid(List<? extends CommonComponentPropertyDTO> components,
      Map<String, String> computerUuidMap, Map<String, String> configUuidMap) {
    for (CommonComponentPropertyDTO componentPropertyDTO : components) {
      String newUUid = getUuid(componentPropertyDTO.getUuid(),computerUuidMap);
      componentPropertyDTO.setUuid(newUUid);
      if (!(componentPropertyDTO instanceof ComputeComponentPropertyDTO)) {
        // 非计算字段，复制时需要重新生成configUuid， uuid 不变
        String newConfigUuid = ReportUuidGenerateUtil.generateReportConfigUuid();
        configUuidMap.put(componentPropertyDTO.getConfigUuid(), newConfigUuid);
        componentPropertyDTO.setConfigUuid(newConfigUuid);
      }
      if (componentPropertyDTO instanceof IndexComponentPropertyDTO) {
        // 指标字段, 如果配置了高级计算，在advanceComputing 去更新新的 uuid 或者configUuid
        IndexComponentPropertyDTO index = (IndexComponentPropertyDTO) componentPropertyDTO;
        if (Objects.nonNull(index.getAdvancedComputing())) {
          AdvancedComputing advance = index.getAdvancedComputing();
          Integer type = advance.getType();
          if (Objects.nonNull(type)) {
            if ("0".equals(type.toString()) || "1".equals(type.toString())) {
              // 同环比, 只需要uuid 不需要configUuid
              String newUuid = computerUuidMap.get(advance.getUuid());
              if (StringUtils.isBlank(newUuid)) {
                throw new BusinessException("报表中指标字段[" + index.getName() + "]高级计算复制失败");
              }
              advance.setUuid(newUuid);
            } else if ("2".equals(type.toString())) {
              // configUuid 和 uuid
              String adUuid = advance.getUuid();
              String adConfigUuid = advance.getConfigUuid();
              if (Objects.nonNull(adUuid) && !"-1".equals(adUuid)) {
                String newUuid = computerUuidMap.get(adUuid);
                String newConfigUuid = configUuidMap.get(adConfigUuid);
                if (StringUtils.isBlank(newUuid) || StringUtils.isBlank(newConfigUuid)) {
                    log.error("报表中指标字段[{}]高级计算复制失败, 高级计算 uuid: {} configUuid: {}",
                            index.getName(), adUuid, adConfigUuid);
                  throw new BusinessException("报表中指标字段[" + index.getName() + "]高级计算复制失败");
                }
                advance.setUuid(newUuid);
                advance.setConfigUuid(newConfigUuid);
              }
            }
          }
        }
      }
    }
  }

  /**
   * 设置字段原始中文名
   * @param dataSetColumns
   * @param computerNameMap
   */
  public void setOriginalName(List<DatasetColumnConfigDTO> dataSetColumns,
      Map<String, String> computerNameMap){
    if (CollUtil.isEmpty(dataSetColumns)){
      return;
    }

    // 数据集字段
    for (DatasetColumnConfigDTO dataSetColumn : dataSetColumns) {
      if ((this.getId()!=null && this.getId().equals(dataSetColumn.getId()))
          || (this.getEnName()!=null&&this.getEnName().equals(dataSetColumn.getEnName()))) {
        this.setOriginalName(dataSetColumn.getName());
        return;
      }
    }

    // 计算字段
    if (this.getEnName()!=null && computerNameMap.get(this.getEnName())!=null) {
      this.setOriginalName(computerNameMap.get(this.getEnName()));
    }
  }
}
