package com.bestpay.bigdata.bi.common.dto.warn;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: ExpectationDesc
 * Package: com.bestpay.bigdata.bi.report.bean
 * Description: 预警规则描述 期望 model
 *
 * <AUTHOR>
 * @Create 2023/8/4 16:38
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("预警规则描述 期望 model")
public class ExpectationDesc {

    @ApiModelProperty(value = "报表中配置数据源的唯一标识")
    private String uuid;

    @ApiModelProperty(value = "报表配置的唯一标识")
    private String configUuid;

    @ApiModelProperty(value = "'field' / 'contrast' 标识维度 'index' / 'overlayIndex' 标识指标")
    private String reportField;

    @ApiModelProperty(value = "日期类型")
    private String dateType;

    @ApiModelProperty(value = "比较 @ScopeFilterTypeEnum")
    private String scopeFilterType;

    @ApiModelProperty(value = "字符串值")
    private String stringValue;

    @ApiModelProperty(value = "对应的值")
    private List<Object> values;

    @ApiModelProperty(value = "字段显示类型")
    private String showTypeName;

    @ApiModelProperty(value = "名称")
    private String name;
}
