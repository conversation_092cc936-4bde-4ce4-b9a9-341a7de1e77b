package com.bestpay.bigdata.bi.common.dto.dataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 数据集实体类
 *
 * <AUTHOR>
 * @since 2021-12-09 10:27:40
 */
@Data
@ApiModel(value = "多维分析数据集请求类")
public class DataSetRequest implements Serializable {

  /**
   * 应用场景code
   */
  @ApiModelProperty(value = "应用场景code",required = true)
  private Integer code;

  /**
   * 组织code
   */
  @ApiModelProperty(value = "组织code",required = true)
  private String orgCode;

}
