package com.bestpay.bigdata.bi.common.dto.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author:gaodingsong
 * @description: 仪表板页面设置model
 * @createTime:2024/5/7 14:24
 * @version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "仪表板页面设置model")
public class DashboardPageSettingModel {
    // 1 表示自适应  2 表示自定义
    @ApiModelProperty(value = "1 表示自适应  2 表示自定义")
    private Integer pageWidthSetting;

    @ApiModelProperty(value = "宽度百分比，当pageWidthSetting == 2 的时候，才会有值")
    private Integer widthPx;

    /**
     * 水印设置
     */
    @ApiModelProperty(value = "水印设置")
    private WatermarkSetting watermarkSetting;

    /**
     * 背景设置
     */
    @ApiModelProperty(value = "背景设置")
    private BgSetting bgSetting;

    /**
     * 栅格设置
     */
    @ApiModelProperty(value = "栅格设置")
    private DashboardGridSetting gridSetting;

}
