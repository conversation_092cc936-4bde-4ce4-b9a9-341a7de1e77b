package com.bestpay.bigdata.bi.common.util;

import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvValidationException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public class CsvUtil {
    public static enum CsvUtilCheckRule{
        // 校验规则1，不允许csv为空
        CSV_NOT_ALLOWED_EMPTY,
        // 校验规则2，需保证每行字段的个数一致
        FIELDS_PER_ROW_IS_CONSISTENT;
    }

    public static List<List<String>> parse(byte[] data, CsvUtilCheckRule... csvUtilCheckRules) throws IOException, CsvValidationException {
        HashSet<CsvUtilCheckRule> csvUtilCheckRulesSet = new HashSet<>(Arrays.asList(csvUtilCheckRules));

        ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
        InputStreamReader reader = new InputStreamReader(inputStream);
        CSVReader csvReader = new CSVReader(reader);

        List<List<String>> rows = new ArrayList<>();

        String[] header = csvReader.readNext();
        if (header!=null){
            rows.add(Arrays.asList(header));

            String[] row;
            while ((row = csvReader.readNext()) != null) {
                lineCheck(rows, header, row, csvUtilCheckRulesSet);// 逐行校验
                rows.add(Arrays.asList(row));
            }
        }

        endCheck(rows, csvUtilCheckRulesSet);// 结尾校验

        return rows;
    }

    private static void lineCheck(List<List<String>> rows, String[] header, String[] row, HashSet<CsvUtilCheckRule> csvUtilCheckRulesSet) throws CsvValidationException {
        if (csvUtilCheckRulesSet.contains(CsvUtilCheckRule.FIELDS_PER_ROW_IS_CONSISTENT)){
            if (header.length != row.length){
                throw new CsvValidationException("csv中行数据字段个数不一致");
            }
        }
    }

    private static void endCheck(List<List<String>> rows, HashSet<CsvUtilCheckRule> csvUtilCheckRulesSet) throws CsvValidationException {
        if (csvUtilCheckRulesSet.contains(CsvUtilCheckRule.CSV_NOT_ALLOWED_EMPTY)){
            if (rows == null || rows.isEmpty()){
                throw new CsvValidationException("csv is blank");
            }
        }
    }
}
