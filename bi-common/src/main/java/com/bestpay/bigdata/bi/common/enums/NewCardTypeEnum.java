package com.bestpay.bigdata.bi.common.enums;

/**
 * <AUTHOR>
 */
public enum NewCardTypeEnum {
    REPORT("report", "图表"),
    FILTER("filter", "筛选器"),
    TEXT("text", "文本"),
    INDEX_TEXT("indexText", "指标文本"),
    EMBED_TEXT_INDEX("embedText", "文本引用指标文本"),
    TAB("tab", "tab"),
    PAGE("page", "page"),;

    private final String code;
    private final String message;

    NewCardTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static NewCardTypeEnum getByCode(String code) {
        for (NewCardTypeEnum cardType : values()) {
            if (cardType.code.equals(code)) {
                return cardType;
            }
        }

        return null; // Code not found
    }
}
