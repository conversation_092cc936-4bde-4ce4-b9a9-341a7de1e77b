package com.bestpay.bigdata.bi.common.request;

import cn.hutool.http.Method;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author：Song
 * @Date：2024/12/18 10:35
 * @Desc:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NorthStarInsightAiRequest  implements Serializable {


 /**
  * 请求方式
  */
 private Method method;

 /**
  * 请求地址
  */
 private String url;

 /**
  * 请求头
  */
 private Map<String, String> headers;

 /**
  * 请求数据
  */
 private Map<String, Object>  data;


}
