package com.bestpay.bigdata.bi.common.dto.report;

import cn.hutool.json.JSONUtil;
import java.util.Map;
import java.util.UUID;

import com.bestpay.bigdata.bi.common.exception.BusinessException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@ApiModel(description = "图表配置")
public class TableConfiguration {

    // {"basicFormat":{"isMergeCell":false,"alignmentType":"center","isWrap":true},"themeStyle":{}}
    @ApiModelProperty(value = "基础格式")
    private BasicFormat basicFormat;

    @ApiModelProperty(value = "主题样式")
    private ThemeStyle themeStyle;


    public static void setUuid(TableConfiguration tableConfigurationObj,
        Map<String, String> computerUuidMap, Map<String, String> configUuidMap) {

        if(tableConfigurationObj==null
            || tableConfigurationObj.getBasicFormat()==null
            || tableConfigurationObj.getBasicFormat().getCustomColumnWidth()==null){
            return;
        }

        for (CustomColumnWidth customColumnWidth : tableConfigurationObj
            .getBasicFormat().getCustomColumnWidth()) {
            if ("度量值".equals(customColumnWidth.getColumnName()) && StringUtils.isBlank(customColumnWidth.getUuid())) {
                continue;
            }
            customColumnWidth.setUuid(getUuid(customColumnWidth.getUuid(), computerUuidMap));
            customColumnWidth.setConfigUuid(getUuid(customColumnWidth.getConfigUuid(), configUuidMap));
        }
    }

    private static String getUuid(String originUuid, Map<String, String> computerUuidMap){
        if(originUuid==null){
            throw new BusinessException("配置字段数据存在异常!");
        }
        String newUuid = computerUuidMap.get(originUuid);
        if(newUuid==null){
            throw new BusinessException("配置字段数据存在异常!");
        }

        return newUuid;
    }
}


