package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 坐标轴Y轴
 * <AUTHOR>
 */
@Data
public class CoordinateAxisXRequest extends BaseFontRequest {
    /**
     * 标签颜色
     */
    private String labelColor;

    /**
     * 标签字符长度
     */
    private Integer labelCharacterLength;

    /**
     * 标签旋转角度
     */
    private Integer labelRotationAngle;


    @ApiModelProperty(value = "轴范围开始")
    private Integer axisRangeStart;

    @ApiModelProperty(value = "轴范围结束")
    private Integer axisRangeEnd;

    //  范围 0-10
    @ApiModelProperty(value = "数据步长")
    private Integer dataStepSize;

    @ApiModelProperty(value = "是否展示网格线")
    private Boolean showGridlines;

    /**
     * 是否展示Y轴
     */
    private Boolean showXAxis;

    public Boolean getShowXAxis() {
        if(this.showXAxis ==null){
            return true;
        }

        return showXAxis;
    }

    public void setShowXAxis(Boolean showXAxis) {
        if(showXAxis ==null){
            this.showXAxis = true;
            return;
        }

        this.showXAxis = showXAxis;
    }
}
