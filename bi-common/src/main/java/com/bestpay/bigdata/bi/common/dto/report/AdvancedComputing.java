package com.bestpay.bigdata.bi.common.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 高级计算
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "高级计算")
public class AdvancedComputing implements Serializable {

  @ApiModelProperty(value = "英文-字段原名",notes = "入参")
  private String originEnName;


  /**
   * 字段显示类型名称
   */
  @ApiModelProperty(value = "底层表字段类型",notes = "入参")
  private String typeName;

  /**
   * 字段显示类型名称
   */
  @ApiModelProperty(value = "字段显示类型名称")
  private String showTypeName;

  /**
   * 计算类型
   */
  @ApiModelProperty(value = "计算类型")
  private Integer type;

  /**
   * 计算字段uuid -- 百分比
   */
  @ApiModelProperty(value = "计算字段uuid, 同环比、百分比都需要uuid")
  private String uuid;


  @ApiModelProperty(value = "报表配置的唯一标识, 只会在百分比情况下需要")
  private String configUuid;

  /**
   * 字段名称
   */
  @ApiModelProperty(value = "字段名称")
  private String name;
  /**
   * 字段英文名称
   */
  @ApiModelProperty(value = "字段英文名称")
  private String enName;

  /**
   * 日期类型维度统计类型：1-日；2-月；3-年；4-周；5-季度
   */
  @ApiModelProperty(value = "日期类型维度统计类型")
  private Integer dateGroupType;

  /**日期截取展示枚举 @DateShowFormatEnum */
  private Integer dateShowFormat;

  /**
   * 所选日期类型：0：（上日、上月、上周、上季、上年）；1：（本日、本月、本周、本季、本年）
   * 与所选日期统计类型对应
   */
  @ApiModelProperty(value = "所选日期类型")
  private Integer dateType;
}
