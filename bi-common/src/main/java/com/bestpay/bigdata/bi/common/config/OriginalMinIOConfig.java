package com.bestpay.bigdata.bi.common.config;

import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.SetBucketPolicyArgs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
@Slf4j
@ConditionalOnProperty(name = "refresh.original-minio-enabled", havingValue = "true")
public class OriginalMinIOConfig {

    @Autowired
    private ApolloRefreshConfig apolloRefreshConfig;

    @Bean
    public MinioClient minioClient() throws Exception {
        return MinioClient
                .builder()
                .endpoint(apolloRefreshConfig.getOriginalMinioEndpoint())
                .credentials(apolloRefreshConfig.getOriginalMinioAccessKey(), apolloRefreshConfig.getOriginalMinioSecretKey())
                .build();
    }

    @PostConstruct
    public void initializeMinIO() {
        try {
            MinioClient client = minioClient();
            ensureBucketExists(client, apolloRefreshConfig.getOriginalMinioBucketName());
            setBucketPolicy(client, apolloRefreshConfig.getOriginalMinioBucketName());
        } catch (Exception e) {
            log.error("Error during MinIO initialization: {}", e.getMessage(), e);
            throw new IllegalStateException("MinIO initialization failed", e);
        }
    }

    private void ensureBucketExists(MinioClient client, String bucketName) throws Exception {
        if (!client.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
            client.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            log.info("Bucket {} created.", bucketName);
        }
    }

    private void setBucketPolicy(MinioClient client, String bucketName) throws Exception {
        String policy = "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetBucketLocation\",\"s3:ListBucket\",\"s3:ListBucketMultipartUploads\"],\"Resource\":[\"arn:aws:s3:::" + bucketName + "\"]},{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:AbortMultipartUpload\",\"s3:DeleteObject\",\"s3:GetObject\",\"s3:ListMultipartUploadParts\",\"s3:PutObject\"],\"Resource\":[\"arn:aws:s3:::" + bucketName + "/*\"]}]}";
        client.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucketName).config(policy).build());
        log.info("Policy set for bucket {}.", bucketName);
    }

}
