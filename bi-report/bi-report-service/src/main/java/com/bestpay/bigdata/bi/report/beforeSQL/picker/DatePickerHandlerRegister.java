package com.bestpay.bigdata.bi.report.beforeSQL.picker;

import com.bestpay.bigdata.bi.report.beforeSQL.enums.DateTypeEnum;
import com.bestpay.bigdata.bi.report.beforeSQL.picker.picker.DatePickerHandler;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 获取所有引擎连接池处理类
 * <AUTHOR>
 */
@Component
@Slf4j
public class DatePickerHandlerRegister implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;
    private static Map<DateTypeEnum, DatePickerHandler> datePickerHandlerMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        log.info("dataSource:loadDataSourceHandler");
        Map<String, DatePickerHandler> beanMap
                = applicationContext.getBeansOfType(DatePickerHandler.class);

        for (DatePickerHandler impl : beanMap.values()) {
            datePickerHandlerMap.put(impl.dateType(), impl);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext){
        this.applicationContext = applicationContext;
    }

    public DatePickerHandler getHandler(DateTypeEnum dateType){
        return datePickerHandlerMap.get(dateType);
    }
}