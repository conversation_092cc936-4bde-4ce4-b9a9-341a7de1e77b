package com.bestpay.bigdata.bi.report.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.error.SQLEngineErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.google.common.collect.Lists;
import com.zaxxer.hikari.HikariDataSource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * ClassName: MysqlDataTransfer Package: com.bestpay.bigdata.bi.report.controller.dataset.correction
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/10/23 14:22
 * @Version 1.0
 */
@Slf4j
@Component
public class MysqlDataTransfer {

  @Resource
  private ApolloRefreshConfig apolloRefreshConfig;

  // 队列不能太长，线程数可以多些，否则线程池关闭了，任务来不及处理完
  private ThreadPoolExecutor executorService = new ThreadPoolExecutor(0, 20,
      60L, TimeUnit.SECONDS,
      new LinkedBlockingQueue<Runnable>(10),
      new BlockCallerPolicy());


  class BlockCallerPolicy implements RejectedExecutionHandler {

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
      try {
        // block until there's room
        executor.getQueue().put(r);
      } catch (InterruptedException e) {
        throw new RejectedExecutionException("Unexpected InterruptedException", e);
      }
    }
  }

  private static final String create_sql_template = "create table %s like %s";
  private static final String insert_sql_template = "insert into %s select * from %s where status_code!=9";
  private static final String drop_sql_template = "drop table if exists %s";
  private static final String select_sql_template = "select * from %s where status_code!=9 limit %s offset %s";
  private static final String truncate_sql_template = "truncate table %s";
  private static final String count_sql_template = "select count(1) from %s where status_code!=9 order by `id`";

  @Transactional(rollbackFor = Exception.class)
  public Response startSyncData() throws InterruptedException {

    HikariDataSource source = null;
    HikariDataSource target = null;

    try {
      source = biSourceDataSource();
      target = biTargetDataSource();

      JdbcTemplate sourceTemplate = new JdbcTemplate();
      sourceTemplate.setDataSource(source);
      JdbcTemplate targetTemplate = new JdbcTemplate();
      targetTemplate.setDataSource(target);

      checkRecoverTable(targetTemplate);

      for (Map.Entry<String, String> entry : apolloRefreshConfig.getWaitSyncTableSet()
          .entrySet()) {

        if ("stop".equalsIgnoreCase(apolloRefreshConfig.getStopSyncTable())) {
          log.info("stop process.........");
          return Response.ok();
        }

        String sourceTable = entry.getKey();
        String bakTable = entry.getValue();
        String targetTable = sourceTable;

        try {
          process(sourceTemplate, targetTemplate, sourceTable, bakTable, targetTable);
        } catch (Exception e) {
          log.info("sync data, table={}, error.........", e);
          continue;
        }

        log.info("sync data, table={}, finished.........", sourceTable);
      }

      // 等待有任务执行完成
      Thread.sleep(60 * 1000);

    } catch (Exception e) {
      log.info("sync data, error.........", e);
      throw e;
    } finally {
      // 关闭DataSource
      if (source != null) {
        source.close();
      }
      if (target != null) {
        target.close();
      }
    }
    return Response.ok();
  }

  private void process(JdbcTemplate sourceTemplate,
      JdbcTemplate targetTemplate,
      String sourceTable,
      String bakTable,
      String targetTable) {

    // Back up tables that need to be synchronized
    targetTemplate.execute(String.format(drop_sql_template, bakTable));
    targetTemplate.execute(
        String.format(create_sql_template, bakTable, targetTable));
    targetTemplate.execute(
        String.format(insert_sql_template, bakTable, targetTable));

    targetTemplate.execute(String.format(truncate_sql_template, targetTable));

    // sync source table data to target table data
    Map<String, Object> countMap = sourceTemplate.queryForList(
        String.format(count_sql_template, sourceTable)).get(0);
    Long count = (Long) new ArrayList<>(countMap.values()).get(0);
    int batch = 1000;

    int processing = 0;
    InsertNeedPart insertNeedPart = null;
    Long size = (count + batch - 1) / batch;;
    for (int cur = 0; cur < size; cur++) {

      int limit = batch;
      int offset = cur * batch;

      List<Map<String, Object>> sourceData = sourceTemplate.queryForList(
          String.format(select_sql_template, sourceTable, limit, offset));

      List<Object[]> argsList = Lists.newArrayList();
      int i = 1;

      for (Map<String, Object> rowData : sourceData) {
        if(insertNeedPart==null) {
          insertNeedPart = generateInsertTemplateSql(rowData,
              targetTable);
        }

        argsList.add(rowData.values().toArray());

        if (i % batch == 0) {
          // 多线程执行插入
          InsertNeedPart finalInsertNeedPart = insertNeedPart;
          List<Object[]> finalArgsList = argsList;
          executorService.submit(() -> {
            try {
              targetTemplate.batchUpdate(finalInsertNeedPart.getInsertSql(),
                  finalArgsList);
            }catch (Exception e){
              log.info("table={}, count={}, error.........", sourceTable, count, e);
              throw e;
            }
          });

          processing = processing + argsList.size();
          log.info("table={}, count={}, already={}, processing.........",
              sourceTable, count, processing);

          argsList = Lists.newArrayList();
        }

        i++;
      }

      // 只有一页的情况， 或者最后一页数据不足批次数量写入
      if(cur == (size-1) && CollUtil.isNotEmpty(argsList)){
        InsertNeedPart finalInsertNeedPart = insertNeedPart;
        List<Object[]> finalArgsList = argsList;
        executorService.submit(() -> {
          try {
            targetTemplate.batchUpdate(finalInsertNeedPart.getInsertSql(),
                finalArgsList);
          }catch (Exception e){
            log.info("table={}, count={}, error.........", sourceTable, count, e);
            throw e;
          }
        });

        processing = processing + argsList.size();
        log.info("table={}, count={}, already={}, processing.........",
            sourceTable, count, processing);
      }

    }
  }


  private void checkRecoverTable(JdbcTemplate targetTemplate) {

    List<String> allTableList = getAllTableList(targetTemplate);

    Optional<String> first = apolloRefreshConfig.getWaitSyncTableSet()
        .values().stream().filter(allTableList::contains).findFirst();
    if (first.isPresent()) {
      throw new BiException(SQLEngineErrorCode.FRIENDLY_TIPS,"建议检查好原始数据，然后请调用恢复接口/biReport/test/clean，删除备份表");
    }
  }


  public List<String> getAllTableList(JdbcTemplate targetTemplate) {

    List<Map<String, Object>> showTables = targetTemplate.queryForList("SHOW TABLES");
    List<String> list = new ArrayList<>();
    for (Map<String, Object> tuple : showTables) {
      for (Map.Entry<String, Object> entry : tuple.entrySet()) {
        list.add(entry.getValue().toString());
      }
    }
    return list;
  }

  public Response clean() {
    JdbcTemplate targetTemplate = new JdbcTemplate();
    targetTemplate.setDataSource(biTargetDataSource());

    for (Map.Entry<String, String> entry : apolloRefreshConfig.getWaitSyncTableSet().entrySet()) {
      String bakTable = entry.getValue();
      try {
        targetTemplate.execute(String.format(drop_sql_template, bakTable));
      } catch (Exception e) {
        log.error("clean mysql data happen error , bakTable " + bakTable, e);
      }
    }
    return Response.ok();
  }


  private InsertNeedPart generateInsertTemplateSql(Map<String, Object> rowData, String table) {
    String insert_template = "insert into %s( %s ) values( %s )";

    StringBuilder placeholder_part = new StringBuilder();
    StringBuilder values_part = new StringBuilder();
    Object[] args = new Object[rowData.size()];

    int index = 0;
    for (Map.Entry<String, Object> entry : rowData.entrySet()) {
      placeholder_part.append(" ? ");
      values_part.append(" `" + entry.getKey() + "` ");
      args[index] = entry.getValue();

      if (index < rowData.size() - 1) {
        placeholder_part.append(" , ");
        values_part.append(" , ");
      }
      index++;
    }
    String insertSql = String.format(insert_template, table, values_part, placeholder_part);
    InsertNeedPart insertNeedPart = new InsertNeedPart();
    insertNeedPart.setInsertSql(insertSql);
    insertNeedPart.setArgs(args);
    return insertNeedPart;
  }

  class InsertNeedPart {

    String insertSql;
    Object[] args;

    public String getInsertSql() {
      return insertSql;
    }

    public void setInsertSql(String insertSql) {
      this.insertSql = insertSql;
    }

    public Object[] getArgs() {
      return args;
    }

    public void setArgs(Object[] args) {
      this.args = args;
    }
  }


  private HikariDataSource biSourceDataSource() {
    HikariDataSource dataSource = new HikariDataSource();

    DataSourceConfig biMysqlConfig = JSONUtil.toBean(apolloRefreshConfig.getBiMysqlDatasource(),
        DataSourceConfig.class);

    dataSource.setDriverClassName(biMysqlConfig.getDriveClass());
    dataSource.setJdbcUrl(biMysqlConfig.getJdbcUrl());
    dataSource.setUsername(biMysqlConfig.getUserName());
    dataSource.setPassword(biMysqlConfig.getPassWord());

    return dataSource;
  }


  private HikariDataSource biTargetDataSource() {
    HikariDataSource dataSource = new HikariDataSource();

    // TODO: 2023/10/24 如果之前指标相关的代码不使用了，可以清除相关代码，另外这里的命名以及apollo的key应该规范
    DataSourceConfig indexMysqlConfig = JSONUtil.toBean(
        apolloRefreshConfig.getIndexMysqlDatasource(),
        DataSourceConfig.class);
    dataSource.setDriverClassName(indexMysqlConfig.getDriveClass());
    dataSource.setJdbcUrl(indexMysqlConfig.getJdbcUrl());
    dataSource.setUsername(indexMysqlConfig.getUserName());
    dataSource.setPassword(indexMysqlConfig.getPassWord());

    return dataSource;
  }


  @Data
  class DataSourceConfig implements Serializable {

    private String driveClass;
    private String jdbcUrl;
    private String userName;
    private String passWord;
    private String validationQuery;
    private Integer maxTotal;
    private Long maxWaitMillis;
    private Boolean testOnBorrow;
    private String jmxBeanName;
  }
}
