package com.bestpay.bigdata.bi.report.request.dashboard;

import com.bestpay.bigdata.bi.common.bean.MoveRequest;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2023-06-05-15:12
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("仪表盘位置移动请求参数")
public class MoveDashboardDirectoryRequest {

    /**
     * 移动之后的集合，需要传递移动之后的 orderNum
     */
    @ApiModelProperty(value = "移动之后的集合，需要传递移动之后的 orderNum")
    List<MoveRequest> postMoveSortList;
}
