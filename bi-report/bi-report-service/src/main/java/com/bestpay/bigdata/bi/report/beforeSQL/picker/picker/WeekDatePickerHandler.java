package com.bestpay.bigdata.bi.report.beforeSQL.picker.picker;

import cn.hutool.core.date.DateUtil;
import com.bestpay.bigdata.bi.report.beforeSQL.enums.DateTypeEnum;
import com.google.common.collect.Lists;
import java.util.Calendar;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class WeekDatePickerHandler extends DatePickerHandler {

  @Override
  List<String> getStartEndTime(String value) {
    List<String> times = Lists.newArrayList();

    // value = 2023-01, 年-周，2023-01，2023年第1周
    String[] values = value.split("-");

    Calendar calendar = Calendar.getInstance();
    calendar.set(Calendar.YEAR, Integer.valueOf(values[0]));
    calendar.set(Calendar.WEEK_OF_YEAR, Integer.valueOf(values[1]));

    times.add(DateUtil.format(DateUtil.beginOfWeek(calendar).getTime(),
        "yyyy-MM-dd HH:mm:ss"));

    times.add(DateUtil.format(DateUtil.endOfWeek(calendar).getTime(),
        "yyyy-MM-dd HH:mm:ss"));

    return times;
  }

  @Override
  public DateTypeEnum dateType() {
    return DateTypeEnum.WEEK;
  }

  public static void main(String[] args){
    WeekDatePickerHandler weekDatePickerHandler = new WeekDatePickerHandler();
//    String value = "2023-01";
    String value = "2023-01";
    System.out.println(weekDatePickerHandler.getStartEndTime(value));
  }
}
