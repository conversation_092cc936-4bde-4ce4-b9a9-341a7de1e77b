package com.bestpay.bigdata.bi.report.schedule.subscribe.enums;

import com.google.common.collect.Lists;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-03-03-18:33
 */
public enum SubscribeType {

  /**
   * EMAIL: 邮件 DING_TALK: 钉钉 SMS: 短信 DING_TALK_PRIVATE: 个人钉钉
   */
  EMAIL(0), DING_TALK(1), SMS(2), DING_TALK_PRIVATE(3);

  public static Map<Integer, List<PostType>> SubTypeSupportPostTypeMap = new HashMap<>();

  static {
    SubTypeSupportPostTypeMap.put(EMAIL.getCode(), Lists.newArrayList(PostType.IMAGE, PostType.PDF));
    SubTypeSupportPostTypeMap.put(DING_TALK.getCode(), Lists.newArrayList(PostType.IMAGE, PostType.LINK));
    SubTypeSupportPostTypeMap.put(DING_TALK_PRIVATE.getCode(), Lists.newArrayList(PostType.IMAGE, PostType.LINK));

  }

  private final int code;

  SubscribeType(int code) {
    this.code = code;
  }

  public int getCode() {
    return code;
  }
}
