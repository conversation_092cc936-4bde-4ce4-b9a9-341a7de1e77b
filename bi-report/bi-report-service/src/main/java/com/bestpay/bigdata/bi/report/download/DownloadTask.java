package com.bestpay.bigdata.bi.report.download;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.config.DataSourceConfig;
import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.common.util.LogTraceIdGenerator;
import com.bestpay.bigdata.bi.database.api.common.DownloadStatService;
import com.bestpay.bigdata.bi.database.bean.DownloadStat;
import com.bestpay.bigdata.bi.report.download.bean.DownloadContext;
import com.bestpay.bigdata.bi.report.download.bean.DownloadResult;
import com.bestpay.bigdata.bi.report.download.bean.DownloadStatusEnum;
import com.bestpay.bigdata.bi.report.download.bean.DownloadTypeEnum;
import com.bestpay.bigdata.bi.report.download.job.DownloadJob;
import com.bestpay.bigdata.bi.report.download.persist.PersistHelper;
import com.bestpay.bigdata.bi.report.download.persist.SftpPersistHelper;
import com.bestpay.bigdata.bi.report.monitor.MonitorManager;
import com.bestpay.bigdata.bi.report.monitor.download.DownloadStatics;
import com.bestpay.bigdata.bi.report.util.ApplicationContextHelper;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;

/**
 * ClassName: DownloadTask
 * Package: com.bestpay.bigdata.bi.report.download
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/12/11 14:37
 * @Version 1.0
 */
@Slf4j
public class DownloadTask implements Runnable, Download
{
    private DownloadJob delegateDownloadTask;
    private volatile DownloadContext context;
    private DataSourceConfig dataSourceConfig;
    private DownloadStatService downloadStatService;

    private String traceId;


    public DownloadTask(DownloadJob delegateDownloadTask, DownloadContext context)
    {
        this.delegateDownloadTask = delegateDownloadTask;
        this.context = context;
        this.dataSourceConfig = ApplicationContextHelper.getBean(DataSourceConfig.class);
        this.downloadStatService = ApplicationContextHelper.getBean(DownloadStatService.class);
        this.traceId = MDC.get(LogTraceIdGenerator.TRACE_ID);
    }

    @Override
    public void run()
    {
        try {
            DownloadStatics.downloadStatics.incrementDownloadRunningTaskCount();
            MDC.put(LogTraceIdGenerator.TRACE_ID, traceId);
            log.info("download task {} start execute", context.getDownloadId());
            context.setStatus(DownloadStatusEnum.RUNNING);
            String localFilePath = getLocalFilePath(dataSourceConfig.getLocalpath());
            log.info("download task {} localFilePath {}", context.getDownloadId(), localFilePath);
            context.setLocalFilePath(localFilePath);
            // todo Determine persistHelper
            SftpPersistHelper persistHelper = new SftpPersistHelper(context.getFileType(), localFilePath);
            log.info("download task {} create persistHelper", context.getDownloadId());
            execute(persistHelper);
            context.setStatus(DownloadStatusEnum.SUCCESS);
        }
        catch (Throwable t) {
            log.error("download task happen error ", t);
            context.setStatus(DownloadStatusEnum.FAIL);
            context.setErrorMessage(ExceptionUtils.getStackTrace(t));
        } finally {
            saveAudit();
            MDC.remove(LogTraceIdGenerator.TRACE_ID);
            DownloadStatics.downloadStatics.incrementDownloadFinishedTaskCount(context.getStatus());
        }
    }

    @Override
    public void execute(PersistHelper persistHelper)
    {
        log.info("download task {} start delegate execute", context.getDownloadId());
        delegateDownloadTask.execute(persistHelper);
    }

    private String getLocalFilePath(String localPath) {
        FileType fileType = context.getFileType() == null ? FileType.EXCEL : context.getFileType();
        String localFilePath = StrUtil.builder().append(localPath).append(StrUtil.SLASH).append(context.getFileIdentifier())
                .append(DateUtil.current()).append(StrUtil.SLASH).append(context.getFileIdentifier())
                .append(DateUtil.current()).append(fileType.getSuffix()).toString();
        log.info("localFilePath:{}", localFilePath);
        return localFilePath;
    }

    private void saveAudit() {
        DownloadStat downloadStat = new DownloadStat();
        log.info("context : {}", context);
        if (Objects.nonNull(context.getFileType())) {
            downloadStat.setFileType(context.getFileType().name());
        }
        downloadStat.setDownloadContent(context.getDownloadContent());
        downloadStat.setEngineQueryId(context.getQueryId());
        downloadStat.setUsername(context.getUsername());
        downloadStat.setStatus(0);
        downloadStat.setSql(context.getStatus().getCode());
        downloadStat.setFileType(context.getFileType().name());
        downloadStat.setFilePath(context.getRemoteFilePath());
        downloadStat.setUserOrg(context.getUserOrg());
        downloadStat.setMessage(context.getErrorMessage());
        downloadStat.setClusterName(context.getClusterName());
        downloadStat.setZipPassword(context.getZipPassword());
        downloadStat.setDownloadContent(context.getDownloadContent());
        downloadStat.setTypeCode(context.getTypeCode());
        downloadStat.setTraceId(MDC.get(LogTraceIdGenerator.TRACE_ID));
        downloadStat.setCreatedBy(context.getUsername());
        downloadStat.setCreatedAt(new Date());
        downloadStat.setUpdatedBy(context.getUsername());
        downloadStat.setUpdatedAt(new Date());
        downloadStatService.insert(downloadStat);
    }

    @Override
    public DownloadTypeEnum getDownloadType()
    {
        return delegateDownloadTask.getDownloadType();
    }

    @Override
    public DownloadStatusEnum getDownloadStatus()
    {
        return context.getStatus();
    }

    @Override
    public String getErrorMessage()
    {
        return context.getErrorMessage();
    }

    @Override
    public DownloadResult getDownloadResult()
    {
        String remoteFilePath = context.getRemoteFilePath();
        String zipPassword = context.getZipPassword();
        DownloadId downloadId = context.getDownloadId();
        String localFilePath = context.getCompressFilePath();

        return new DownloadResult(downloadId, remoteFilePath, localFilePath, zipPassword);
    }
}
