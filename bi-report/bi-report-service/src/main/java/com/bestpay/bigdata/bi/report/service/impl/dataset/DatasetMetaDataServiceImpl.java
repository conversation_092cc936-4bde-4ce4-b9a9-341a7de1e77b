package com.bestpay.bigdata.bi.report.service.impl.dataset;

import static com.bestpay.bigdata.bi.common.enums.CodeEnum.META_FIELDS_QUERY_FAIL;
import static java.util.Objects.requireNonNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetElementQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetFieldDeleteDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetFieldQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetQueryDTO;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.DatasetErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.request.metaData.MetaDataQueryDatabaseRequest;
import com.bestpay.bigdata.bi.common.request.metaData.MetaDataQueryFieldRequest;
import com.bestpay.bigdata.bi.common.request.metaData.MetaDataQueryTableRequest;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.response.metaData.MetaDataDBResponse;
import com.bestpay.bigdata.bi.common.response.metaData.MetaDataField;
import com.bestpay.bigdata.bi.common.util.AssertUtil;
import com.bestpay.bigdata.bi.database.api.dataset.DatasetDAOService;
import com.bestpay.bigdata.bi.database.api.dataset.DatasetElementDAOService;
import com.bestpay.bigdata.bi.database.api.dataset.DatasetFieldDAOService;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetDo;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetElementDo;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetFieldDo;
import com.bestpay.bigdata.bi.report.cache.CacheHandleFactory;
import com.bestpay.bigdata.bi.report.cache.CommonCacheHandler;
import com.bestpay.bigdata.bi.report.cache.bean.DatasetCacheBean;
import com.bestpay.bigdata.bi.report.cache.enums.SceneEnums;
import com.bestpay.bigdata.bi.report.request.dataset.MetaSyncRequest;
import com.bestpay.bigdata.bi.report.request.dataset.MetadataRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetMetaDataService;
import com.bestpay.bigdata.bi.thirdparty.api.MetaDataService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DatasetMetaDataServiceImpl implements DatasetMetaDataService {

    @Resource
    private MetaDataService metaDataService;

    @Resource
    private DatasetFieldDAOService fieldDAOService;

    @Resource
    private DatasetDAOService datasetDAOService;

    @Resource
    private DatasetElementDAOService elementDAOService;

    @Resource
    private CacheHandleFactory cacheHandleFactory;

    /**
     * 元数据同步
     * @param request
     * @return
     */
    @Override
    public Response<String> metaSync(MetaSyncRequest request){
        // 1、查询数据集信息
        List<DatasetDo> datasetDos = getDatasetDos(request);

        // 2、查询element信息
        List<DatasetElementDo> elementDos = getElementDos(datasetDos);

        // 3、生成元数据同步请求
        List<MetaDataQueryFieldRequest> metaRequests = generateMetaDataRequest(datasetDos, elementDos);

        // 4、执行同步
        boolean isSuccess = multipleSyncMeta(metaRequests);
        if(!isSuccess){
            Response.error(META_FIELDS_QUERY_FAIL);
        }

        return Response.ok();
    }

    /**
     * 查询数据集 element信息
     * @param datasetDos
     * @return
     */
    private List<DatasetElementDo> getElementDos(List<DatasetDo> datasetDos) {
        DatasetElementQueryDTO elementQuery = DatasetElementQueryDTO.builder()
                .datasetCodeList(datasetDos.stream().map(p->p.getCode()).collect(Collectors.toList()))
                .build();

        if(CollUtil.isEmpty(elementQuery.getDatasetCodeList())){
            log.info("elementQuery = {} is empty", JSONUtil.toJsonStr(elementQuery));
            return Lists.newArrayList();
        }

        List<DatasetElementDo> elementDos = elementDAOService.query(elementQuery);

        log.info("elementQuery={}, elementDos={}",
                JSONUtil.toJsonStr(elementQuery), JSONUtil.toJsonStr(elementDos));

        return elementDos;
    }

    /**
     * 查询数据集信息 并失效缓存
     * @param request
     * @return
     */
    private List<DatasetDo> getDatasetDos(MetaSyncRequest request) {
        AssertUtil.notEmpty(request.getIds(), CodeEnum.DATASET_IDS_BLANK);

        DatasetQueryDTO datasetQuery = DatasetQueryDTO.builder()
                .idList(request.getIds())
                .build();

        List<DatasetDo> datasetDos = datasetDAOService.query(datasetQuery);

        datasetDos.forEach(e -> {
            // 失效数据集缓存
            CommonCacheHandler handleStrategy = cacheHandleFactory.getHandleStrategy(SceneEnums.DATA_SET);
            DatasetCacheBean cacheBean = DatasetCacheBean.builder().cacheKey(String.valueOf(e.getId())).build();
            handleStrategy.invalidCache(cacheBean);
        });

        log.info("datasetQuery={}, datasetDos={}",
                JSONUtil.toJsonStr(request.getIds()), JSONUtil.toJsonStr(datasetQuery));

        return datasetDos;
    }

    /**
     * 返回表下面的字段信息
     * @param requests
     * @return
     */
    @Override
    public Map<String, List<DatasetFieldDo>> getMetaFields(List<MetaDataQueryFieldRequest> requests){
        if(CollUtil.isEmpty(requests)){
            log.info("requests is empty");
            return Maps.newHashMap();
        }

        Map<String, List<DatasetFieldDo>> fieldsMap = Maps.newHashMap();
        for (MetaDataQueryFieldRequest request : requests) {
            try {
                try {
                    // 1、同步元数据字段信息
                    try {
                        singleSyncMetaData(request);
                    }catch (Exception e){
                        // 元数据同步失败, 使用历史同步数据
                        log.error("syncMetaData fail, request={}", JSONUtil.toJsonStr(request),e);
                    }

                    // 2、获取db最新的字段信息
                    List<DatasetFieldDo> dbFields = fieldDAOService.find(getFieldsQuery(request));
                    if(CollUtil.isEmpty(dbFields)){
                        log.info("db 字段信息为空, request={}", JSONUtil.toJsonStr(request));
                        continue;
                    }

                    fieldsMap.put(request.getUniqueKey(), dbFields);
                }catch (Exception e){
                    log.error("getMetaFields fail, request={}", JSONUtil.toJsonStr(request));
                }

            }catch (Exception e){
                log.error("从元数据获取字段信息失败",e);
            }
        }

        return fieldsMap;
    }


    /**
     * 查询元数据库信息
     *
     * @param metadataRequest
     * @return
     */
    @Override
    public Response<List<String>> queryMetaDatabases(MetadataRequest metadataRequest) {
        requireNonNull(metadataRequest.getDatasourceType(), "datasource type can not be null");
        requireNonNull(metadataRequest.getDatasourceName(), "datasource name can not be null");

        // create url by datasource type
        MetaDataQueryDatabaseRequest request = MetaDataQueryDatabaseRequest.builder()
                .datasourceType(metadataRequest.getDatasourceType()).build();

        if ("mysql".equalsIgnoreCase(metadataRequest.getDatasourceType())
                || "oceanbase".equalsIgnoreCase(metadataRequest.getDatasourceType())
                || "postgresql".equalsIgnoreCase(metadataRequest.getDatasourceType())) {
            request.setDatasourceUseType("read");
        }

        // request metadata
        // 记录一下：hive数据源、元数据接口请求参数需要带上数据源名称, 其他源不需要
        request.setDatasourceName(metadataRequest.getDatasourceName());
        List<MetaDataDBResponse> metaDataDBResponseList = metaDataService.metaDataQueryDatabase(request);

        // return to front
        List<String> result = Lists.newArrayList();
        for (MetaDataDBResponse metaDataDBResponse : metaDataDBResponseList) {
            if (metaDataDBResponse.getDatasourceName().equals(metadataRequest.getDatasourceName())) {
                result.addAll(metaDataDBResponse.getDbNames());
            }
        }

        return Response.ok(result);
    }


    /**
     * 查询元数据表信息
     *
     * @param metadataRequest
     * @return
     */
    @Override
    public Response<List<String>> queryMetaDataTables(MetadataRequest metadataRequest) {
        requireNonNull(metadataRequest.getDatasourceType(), "datasource type can not be null");
        requireNonNull(metadataRequest.getDatasourceName(), "datasource name can not be null");
        requireNonNull(metadataRequest.getDatabaseName(), "database name can not be null");

        MetaDataQueryTableRequest request = MetaDataQueryTableRequest.builder()
                .datasourceType(metadataRequest.getDatasourceType())
                .datasourceName(metadataRequest.getDatasourceName())
                .dbName(metadataRequest.getDatabaseName()).build();

        List<String> tables = metaDataService.metaDataQueryTable(request);

        return Response.ok(tables);
    }



    private List<MetaDataQueryFieldRequest> generateMetaDataRequest(List<DatasetDo> datasetDos,
                                                                    List<DatasetElementDo> elementDos) {

        if(CollUtil.isEmpty(elementDos)){
            log.info("elementDos is empty");
            return Lists.newArrayList();
        }

        List<MetaDataQueryFieldRequest> metaRequests = Lists.newArrayList();
        for (DatasetElementDo elementDo : elementDos) {
            for (DatasetDo datasetDo : datasetDos) {
                if(elementDo.getDatasetCode().equals(datasetDo.getCode())){
                    String[] dbTableName = elementDo.getName().split("\\.");
                    MetaDataQueryFieldRequest queryFieldRequest = MetaDataQueryFieldRequest.builder()
                            .datasourceType(datasetDo.getDatasourceType())
                            .datasourceName(datasetDo.getDatasourceName())
                            .dbName(dbTableName[0])
                            .tableName(dbTableName[1])
                            .build();
                    metaRequests.add(queryFieldRequest);
                }
            }
        }

        log.info("generateMetaDataRequest, metaRequests={}", JSONUtil.toJsonStr(metaRequests));
        return metaRequests;
    }

    private boolean multipleSyncMeta(List<MetaDataQueryFieldRequest> metaRequests) {
        if(CollUtil.isEmpty(metaRequests)){
            log.info("metaRequests is empty");
            return false;
        }

        // 是否同步成功
        boolean isSuccess = true;

        // 同步请求去重
        List<MetaDataQueryFieldRequest> distinctMetaRequests = metaRequests.stream()
                .filter(distinctByKey(b->b.getUniqueKey()))
                .collect(Collectors.toList());

        for (MetaDataQueryFieldRequest metaDataRequest : distinctMetaRequests) {
            try {

                // 同步元数据字段信息
                log.error("singleSyncMetaData start, metaDataRequest={}", JSONUtil.toJsonStr(metaDataRequest));
                singleSyncMetaData(metaDataRequest);
                log.error("singleSyncMetaData finish");

            } catch (Exception e){
                // 异常继续同步，但是总体是失败的
                isSuccess = false;
                log.error("singleSyncMetaData fail, metaDataRequest={}",
                        JSONUtil.toJsonStr(metaDataRequest), e);
            }
        }

        log.error("singleSyncMetaData state, isSuccess={}", isSuccess);
        return isSuccess;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object,Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    private void singleSyncMetaData(MetaDataQueryFieldRequest request) {
        log.info("syncMetaData start, request={}", JSONUtil.toJsonStr(request));

        // 1、查询元数据获取字段信息
        List<MetaDataField> fieldList = metaDataService.metaDataQueryFieldByTable(request);
        if(CollUtil.isEmpty(fieldList)){
            log.error("元数据字段数据为空, request={}", JSONUtil.toJsonStr(request));
            throw new BiException(DatasetErrorCode.DATASET_MATE_DATA_ERROR,"元数据字段信息查询失败!");
        }

        // 2、updateRequest：待同步数据信息
        List<DatasetFieldDo> updateRequest = fieldList.stream()
                .map(p-> metaDataFieldToBiFieldDo(request, p)).collect(Collectors.toList());

        // 3、deleteRequest：比较差异, 删除字段
        DatasetFieldDeleteDTO deleteRequest = delete(request, fieldList);

        // 4、更新到数据库
        fieldDAOService.insertOrUpdateAndDelete(updateRequest, deleteRequest);

        log.info("syncMetaData finish");
    }

    private DatasetFieldDeleteDTO delete(MetaDataQueryFieldRequest request,
                                         List<MetaDataField> fieldList) {
        // 1、查询db所有字段
        List<DatasetFieldDo> dbFields = fieldDAOService.find(getFieldsQuery(request));
        if(CollUtil.isEmpty(dbFields)){
            log.info("数据库字段数据为空, query={}", JSONUtil.toJsonStr(request));
            return null;
        }

        // 2、MetaFields：元数据实时拿到的字段
        Set<String> fields = fieldList.stream().map(p->p.getColumnName()).collect(Collectors.toSet());

        // 3、比较差异
        List<String> removeFields = dbFields.stream()
                .filter(p->!fields.contains(p.getFieldEnName()))
                .map(p->p.getFieldEnName())
                .collect(Collectors.toList());

        if(CollUtil.isEmpty(removeFields)){
            log.info("无差异");
            return null;
        }

        // 4、删除不存在的字段
        return getFieldDeleteDTO(request, removeFields);
    }

    private DatasetFieldDeleteDTO getFieldDeleteDTO(MetaDataQueryFieldRequest request,List<String> removeFields){
        DatasetFieldDeleteDTO deleteDTO = new DatasetFieldDeleteDTO();
        deleteDTO.setDatasourceType(request.getDatasourceType());
        deleteDTO.setDatasourceName(request.getDatasourceName());
        deleteDTO.setDatabaseName(request.getDbName());
        deleteDTO.setTableName(request.getTableName());
        deleteDTO.setIdList(removeFields);
        return deleteDTO;
    }

    private DatasetFieldDo metaDataFieldToBiFieldDo(MetaDataQueryFieldRequest request, MetaDataField field){
        DatasetFieldDo dataField = new DatasetFieldDo();
        dataField.setDatasourceType(request.getDatasourceType());
        dataField.setDatasourceName(request.getDatasourceName());
        dataField.setDatabaseName(request.getDbName());
        dataField.setTableName(request.getTableName());
        dataField.setFieldCnName(getFieldCnName(field));
        dataField.setFieldEnName(field.getColumnName());
        dataField.setFieldComment(field.getColumnComment());
        dataField.setFieldOriginType(field.getColumnType());
        dataField.setStatusCode(StatusCodeEnum.ONLINE.getCode());
        dataField.setUpdatedAt(new Date());
        dataField.setUpdatedBy("sys");
        dataField.setCreatedAt(new Date());
        dataField.setCreatedBy("sys");
        dataField.setIndex(field.getColumnIndex());
        return dataField;
    }

    private String getFieldCnName(MetaDataField field) {
        if (StringUtils.isNoneEmpty(field.getColumnChineseName())) {
            return field.getColumnChineseName();
        }
        if (StringUtils.isNoneEmpty(field.getColumnComment())) {
            return field.getColumnComment();
        }
        return field.getColumnName();
    }


    private DatasetFieldQueryDTO getFieldsQuery(MetaDataQueryFieldRequest request){
        DatasetFieldQueryDTO query = new DatasetFieldQueryDTO();
        query.setDatasourceType(request.getDatasourceType());
        query.setDatasourceName(request.getDatasourceName());
        query.setDatabaseName(request.getDbName());
        query.setTableName(request.getTableName());
        return query;
    }
}
