package com.bestpay.bigdata.bi.report.bean.dataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author:gaodingsong
 * @description:
 * @createTime:2024/4/17 16:33
 * @version:1.0
 */
@Data
@ApiModel(description = "配置刷新")
public class ConfigRefreshVO {

    @ApiModelProperty("数据集配置")
    private List<DatasetConfigVO> datasetConfigVOList;

    @ApiModelProperty("计算列")
    private List<ComputeColumnDTO> computeColumnList;
}
