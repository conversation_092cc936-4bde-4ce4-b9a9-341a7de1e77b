package com.bestpay.bigdata.bi.report.request.tablecard;

import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
import com.bestpay.bigdata.bi.common.dto.report.component.OrderComponentDTO;
import com.bestpay.bigdata.bi.report.bean.report.RollAndDownDTO;
import com.bestpay.bigdata.bi.report.enums.common.DataRequestTypeEnum;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "表格卡片数据")
public class TableCardDataRequest {
    @ApiModelProperty(value = "仪表板ID")
    private Long dashboardId;
    @ApiModelProperty(value = "卡片code")
    private String cardCode;
    @ApiModelProperty(value = "卡片类型")
    private String cardType;
    @ApiModelProperty(value = "页数")
    private Long pageNum;
    @ApiModelProperty(value = "每页数量")
    private Long pageSize;
    @ApiModelProperty(value = "来源标识")
    private String fromFlag;
    @ApiModelProperty(value = "排序组件")
    private List<OrderComponentDTO> orderColumns;
    @ApiModelProperty(value = "查询条件")
    private List<QueryReportConditionInfo> queryConditions;
    @ApiModelProperty(value = "参数条件")
    private List<QueryReportConditionInfo> paramConditions;
    @ApiModelProperty(value = "是否实时")
    private Boolean isRealtime;
    @ApiModelProperty(value = "显示类型默认查询数据")
    private String show = DataRequestTypeEnum.DATA.getCode();

    @ApiModelProperty(value = "上卷下钻请求")
    private RollAndDownDTO rollAndDownRequest;
    @ApiModelProperty(value = "是否不需要统计")
    private Boolean noCount;
}
