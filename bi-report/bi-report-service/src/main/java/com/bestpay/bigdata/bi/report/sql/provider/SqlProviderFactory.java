package com.bestpay.bigdata.bi.report.sql.provider;

import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import com.bestpay.bigdata.bi.report.sql.bean.SqlContext;
import com.bestpay.bigdata.bi.report.util.InstancedUtil;
import org.reflections.Reflections;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-04-06-9:14
 */
@Component
public class SqlProviderFactory {

    private static Map<SQLEngine, Class<? extends AbstractSqlProvider>> engineTypeSqlProviderMap = new HashMap<>();

    static {
        Reflections reflections = new Reflections(AbstractSqlProvider.class.getPackage().getName());
        for (Class<? extends AbstractSqlProvider> sqlProvider : reflections.getSubTypesOf(AbstractSqlProvider.class)) {
            String simpleName = sqlProvider.getSimpleName();
            String engineType = simpleName.substring(0, (simpleName.length() - "SqlProvider".length()));
            engineTypeSqlProviderMap.put(SQLEngine.valueOf(engineType.toUpperCase()), sqlProvider);
        }
    }

    public SqlProvider createSqlProvider(SQLEngine engine, SqlContext sqlContext) {
        Class<? extends AbstractSqlProvider> clazz = engineTypeSqlProviderMap.get(engine);
        return InstancedUtil.createNewClassInstance(clazz, new Class[]{SqlContext.class}, new Object[]{sqlContext});
    }
}
