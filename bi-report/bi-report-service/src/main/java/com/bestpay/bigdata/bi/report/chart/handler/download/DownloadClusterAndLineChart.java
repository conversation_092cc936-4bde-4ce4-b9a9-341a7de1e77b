package com.bestpay.bigdata.bi.report.chart.handler.download;

import com.bestpay.bigdata.bi.backend.api.QueryService;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.report.chart.bean.ChartContext;
import com.bestpay.bigdata.bi.report.chart.bean.ChartData;
import com.bestpay.bigdata.bi.report.chart.bean.ChartHeader;
import com.bestpay.bigdata.bi.report.chart.bean.ReportBaseModel;
import com.bestpay.bigdata.bi.report.chart.handler.AbstractChart;
import com.bestpay.bigdata.bi.report.chart.handler.query.ListTableChart;
import com.bestpay.bigdata.bi.report.enums.report.ReportFieldEnum;
import com.bestpay.bigdata.bi.report.service.common.EngineDataQueryService;
import com.bestpay.bigdata.bi.report.sql.SemanticManager;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 下载簇状图数据获取
 *
 * <AUTHOR>
 */
public class DownloadClusterAndLineChart extends AbstractChart {

  SemanticManager semanticManager;

  public DownloadClusterAndLineChart(ChartContext context,
      QueryService queryService,
      SemanticManager semanticManager,
      EngineDataQueryService dataQueryService) {
    super(context, queryService, dataQueryService);
    this.semanticManager = semanticManager;
  }

  @Override
  protected ChartData makeChartData(ChartHeader header, List<List<String>> baseData) {
    ReportBaseModel reportBaseModel = context.getReportBaseModel();
    //和ListTableChart一致
    return reportBaseModel.getContrastColumnList().size() > 1 ?
        new DownloadContrastTableChart(context, queryService,semanticManager , dataQueryService).getChartData(header, baseData) :
        new DownloadListTableChart(context, queryService, dataQueryService).getChartData(header, baseData);
  }

  @Override
  public ChartTypeEnum getChartType() {
    return ChartTypeEnum.CLUSTER_AND_LINE_CHART;
  }

  @Override
  public ChartHeader getChartHeader() {
    ReportBaseModel reportBaseModel = context.getReportBaseModel();
    //这边对指标和叠加指标进行排序，一定要保证叠加指标在指标之后，这样才能保证叠加指标下载的时候会在指标后面
    List<IndexComponentPropertyDTO> indexColumnList = reportBaseModel.getIndexColumnList();
    List<IndexComponentPropertyDTO> resultIndexColumnList = indexColumnList.stream()
        .filter(d-> !ReportFieldEnum.OVERLAY_INDEX_FIELD.getCode().equals(d.getReportField())).collect(Collectors.toList());
    resultIndexColumnList.addAll(indexColumnList.stream()
        .filter(d-> ReportFieldEnum.OVERLAY_INDEX_FIELD.getCode().equals(d.getReportField())).collect(Collectors.toList()));
    reportBaseModel.setIndexColumnList(resultIndexColumnList);
    context.setReportBaseModel(reportBaseModel);

    //和直接转换成ListTableChart一致
    return reportBaseModel.getContrastColumnList().size() > 1 ?
        new DownloadContrastTableChart(context, queryService, semanticManager, dataQueryService).getChartHeader() :
        new DownloadListTableChart(context, queryService, dataQueryService).getChartHeader();
  }
}
