package com.bestpay.bigdata.bi.report.schedule.subscribe.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.config.DataSourceConfig;
import com.bestpay.bigdata.bi.common.dto.common.DatePickerDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.report.ColumnPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.common.enums.ReportResourceTypeEnum;
import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.ReportErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.report.bean.subscribe.SubscribeSendDetailDTO;
import com.bestpay.bigdata.bi.report.beforeSQL.util.DatePickerUtil;
import com.bestpay.bigdata.bi.report.download.persist.PersistHelper;
import com.bestpay.bigdata.bi.report.enums.download.FileFormatTypeEnum;
import com.bestpay.bigdata.bi.report.enums.report.DateGroupEnum;
import com.bestpay.bigdata.bi.report.enums.report.ReportFieldEnum;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.CreateSubscribeContext;
import com.bestpay.bigdata.bi.report.schedule.sender.handler.SubscribeTypeSender;
import com.bestpay.bigdata.bi.report.schedule.subscribe.bean.SubscribeContent;
import com.bestpay.bigdata.bi.report.schedule.subscribe.bean.SubscribeContext;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.service.report.ReportDataExportService;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.bestpay.bigdata.bi.report.util.DataFileLocalPathUtil;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.google.common.collect.Lists;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BiReportSubscribe extends AbstractSubscribe {

    @Resource
    private ReportDataExportService reportDataExportService;

    @Resource
    private ReportUpdateService reportUpdateService;

    @Resource
    private DataSourceConfig dataSourceConfig;

    private final static String datePicker = "datePicker";

    @Resource
    private DatasetService datasetService;

    @Resource
    private DatePickerUtil datePickerUtil;

    @Resource
    private DatePickerDAOService pickerDAOService;

    @Resource
    private AiplusService aiplusService;

    @Override
    public void preSubscribe(SubscribeContext subscribeContext) {

    }

    @Override
    public void processSubscribe(SubscribeContext context) throws Exception {

        Report report = reportUpdateService.queryById(context.getObjectId());
        if (report == null) {
            throw new BiException(ReportErrorCode.REPORT_NOT_EXISTS,
                String.format("报表已删除, id=%s", context.getObjectId()));
        }

        context.setSubscribeName(report.getReportName());
        if (StatusCodeEnum.OFFLINE.getCode()==report.getStatusCode()) {
            throw new BiException(ReportErrorCode.REPORT_DATASET_STATUS_ERROR,
                String.format("报表已下线, id=%s", context.getObjectId()));
        }

        FileType excel = FileType.EXCEL;

        // 文件本地保存路径

        LocalDate date = LocalDate.now().minusDays(1);

        String localFilePath = DataFileLocalPathUtil.getLocalFilePath(
            dataSourceConfig.getLocalpath()+"/"+context.getSubCode(),
            excel,
            context.getFileName()+"_"
                +date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        try {
            UserInfo userInfo
                = aiplusService.getUserInfoByEmail(context.getCreatedBy());

            UserContextUtil.preHandler(userInfo);

            // 筛选条件
            DownloadApplyRequest downloadApplyRequest = new DownloadApplyRequest();
            List<ColumnPropertyDTO> conditions
                = JSONUtil.toList(context.getCondition(), ColumnPropertyDTO.class);


            // 参数校验
            checkParam(report, conditions);

            downloadApplyRequest.setParamConditions(getParamConditions(conditions));
            downloadApplyRequest.setQueryConditions(getNonParamConditions(conditions));
            downloadApplyRequest.setIsAll(true);
            downloadApplyRequest.setType(ReportResourceTypeEnum.REPORT.getCode());
            downloadApplyRequest.setReportId(context.getObjectId());

            // 下载到本地
            PersistHelper persistHelper
                = new PersistHelper(excel, localFilePath) {
                @Override
                public String persistToRemote(String localFilePath) {
                    return null;
                }
            };



            // 执行文件生成
            reportDataExportService.generateDataFile(downloadApplyRequest,
                report,
                userInfo,
                persistHelper,
                excel,
                true,
                    FileFormatTypeEnum.FORMAT_WITH_UNIT);

            // 生成的文件
            File file = new File(localFilePath);

            // 发送请求
            CreateSubscribeContext createSubscribeContext =
                CreateSubscribeContext.builder()
//                    .fileName(FileUtil.getPrefix(file))
                    .localPath(file.getParent() + "/")
                    .postType(context.getPostType())
                    .subscribeName(context.getSubscribeName())
                    .file(file)
                        .fileNameList(Stream.of(FileUtil.getPrefix(file)).collect(Collectors.toList()))
                    .build();
            // 发送渠道
            SubscribeContent subscribeContent = getSubscribeTypeSender(context.getSubscribeType())
                .createContentByPostType(createSubscribeContext);
            log.info("报表订阅.SubscribeContent：{}",JSONUtil.toJsonStr(subscribeContent));

            // 执行发送，获取发送结果
            SubscribeTypeSender sender = getSubscribeTypeSender(context.getSubscribeType());
            List<SubscribeSendDetailDTO> sendResList =
                sender.subscribeSend(context.getSubscribeTypeBean(), subscribeContent);

            log.info("订阅结果信息：{}",JSONUtil.toJsonStr(sendResList));

            context.setSendDetailList(sendResList);
            log.info("订阅结果保存入上下文：{}",JSONUtil.toJsonStr(context));

        }catch (Exception e){

            log.error("subscribe runnable fail", e);
//            FailureUtil.setFailInfo(context, ExceptionUtil.getExceptionInfo("", e));
            throw e;
        }finally {
            UserContextUtil.removeHandler();
            // 删除文件
            Path file = Paths.get(localFilePath);
            if(Files.exists(file)) {
                Files.delete(file);
            }
        }
    }



    private void checkParam(Report report, List<ColumnPropertyDTO> conditions) {
        if (CollUtil.isEmpty(conditions)) {
            return;
        }

        // 校验数据集字段属性
        checkDataSetFieldAttr(report, conditions);

        // 校验计算字段属性
        checkComputeFieldAttr(report, conditions);
    }

    private void checkDataSetFieldAttr(Report report, List<ColumnPropertyDTO> conditions) {
        // 获取数据集字段
        List<DatasetInfo> datasetInfoList = JSONUtil.toList(report.getDatasetInfo(),
            DatasetInfo.class);

        DatasetColumnConfigRequest metadataRequest = new DatasetColumnConfigRequest();
        metadataRequest.setDatasetId(datasetInfoList.get(0).getDatasetId());
        List<DatasetColumnConfigDTO> columnDTOList
            = datasetService.getColumnConfigList(metadataRequest).getData();

        if (CollUtil.isEmpty(columnDTOList)) {
            return;
        }

        // 与原始的数据集字段对比
        for (ColumnPropertyDTO filterColumn : conditions) {
            DatasetColumnConfigDTO dataSetField
                    = getDataSetField(columnDTOList, filterColumn);
            try {
                if (dataSetField == null) {
                    continue;
                }

                // 计算集计算字段
                if(BooleanUtil.isTrue(dataSetField.getIsComputeField())){

                    if (filterColumn.getFun()!=null
                        && !filterColumn.getFun().equals(dataSetField.getFun())) {
                        throw new BiException(ReportErrorCode.REPORT_CONF_INFO_CHANGED,String.format("筛选器 %s 属性变化, 原始为 %s, 现在为 %s",
                                filterColumn.getName(),
                                dataSetField.getFun(),
                                filterColumn.getFun()));
                    }

                    if (filterColumn.getFieldType()!=null
                        &&filterColumn.getFieldType().equals(dataSetField.getFieldType())) {
                        throw new BiException(ReportErrorCode.REPORT_CONF_INFO_CHANGED,String.format("筛选器 %s 属性变化, 原始为 %s, 现在为 %s",
                                filterColumn.getName(),
                                dataSetField.getFieldType(),
                                filterColumn.getFieldType()));
                    }


                }else{
                    if (filterColumn.getShowTypeName()!=null
                        &&!filterColumn.getShowTypeName().equals(dataSetField.getShowTypeName())) {
                        throw new BiException(ReportErrorCode.REPORT_CONF_INFO_CHANGED,String.format("筛选器 %s 属性变化, 原始为 %s, 现在为 %s",
                                filterColumn.getName(),
                                dataSetField.getShowTypeName(),
                                filterColumn.getShowTypeName()));
                    }

                    if (filterColumn.getFieldType()!=null
                        &&!filterColumn.getFieldType().equals(dataSetField.getFieldType().name())) {
                        throw new BiException(ReportErrorCode.REPORT_CONF_INFO_CHANGED,String.format("筛选器 %s 属性变化, 原始为 %s, 现在为 %s",
                                filterColumn.getName(),
                                dataSetField.getFieldType(),
                                filterColumn.getFieldType()));
                    }
                }

            }catch (Exception e){
                log.error("checkDataSetFieldAtt.dataSetField:{}, filterColumn={}",JSONUtil.toJsonStr(dataSetField),JSONUtil.toJsonStr(filterColumn));
                throw e;
            }


        }

    }

    private void checkComputeFieldAttr(Report report, List<ColumnPropertyDTO> conditions) {
        // 报表所有计算字段
        List<ComputeComponentPropertyDTO> computeColumnList
            = JSONUtil.toList(report.getComputeColumn(), ComputeComponentPropertyDTO.class);

        if (CollUtil.isEmpty(computeColumnList)) {
            return;
        }

        // 与原始的计算字段做比较
        for (ColumnPropertyDTO filterColumn : conditions) {

            ComputeComponentPropertyDTO computerField
                = getComputeField(computeColumnList, filterColumn);

            if (computerField == null) {
                continue;
            }

            if (filterColumn.getFun()!=null
                && !filterColumn.getFun().equals(computerField.getFun())) {
                throw new BiException(ReportErrorCode.REPORT_CONF_INFO_CHANGED,String.format("筛选器 %s 属性变化, 原始为 %s, 现在为 %s",
                    filterColumn.getName(),
                    computerField.getFun(),
                    filterColumn.getFun()));
            }

            if (filterColumn.getFieldType()!=null
                && !filterColumn.getFieldType().equals(computerField.getFieldType())) {
                throw new BiException(ReportErrorCode.REPORT_CONF_INFO_CHANGED,String.format("筛选器 %s 属性变化, 原始为 %s, 现在为 %s",
                    filterColumn.getName(),
                    computerField.getFieldType(),
                    filterColumn.getFieldType()));
            }
        }
    }



    /**
     * 获取参数查询条件
     * @return
     */
    private List<QueryReportConditionInfo> getParamConditions(List<ColumnPropertyDTO> conditions) {
        return CollUtil.isNotEmpty(conditions)
            ? handlerCondition(conditions.stream()
            .filter(p -> ReportFieldEnum.PARAM.getCode().equalsIgnoreCase(p.getReportField()))
            .collect(
                Collectors.toList())) : Lists.newArrayList();
    }

    /**
     * 获取非参数查询条件
     * @return
     */
    private List<QueryReportConditionInfo> getNonParamConditions(List<ColumnPropertyDTO> conditions) {
        return CollUtil.isNotEmpty(conditions)
            ? handlerCondition(conditions.stream()
            .filter(p -> !ReportFieldEnum.PARAM.getCode().equalsIgnoreCase(p.getReportField()))
            .collect(
                Collectors.toList())) : Lists.newArrayList();
    }

    private List<QueryReportConditionInfo> handlerCondition(List<ColumnPropertyDTO> conditions){

        if(CollUtil.isEmpty(conditions)){
            return Lists.newArrayList();
        }

        List<QueryReportConditionInfo> queryConditions = Lists.newArrayList();
        for (ColumnPropertyDTO columnProperty : conditions) {
            if(columnProperty.getScreeningCondition()==null){
                continue;
            }

            QueryReportConditionInfo queryCondition = getBaseQueryCondition(columnProperty);
            if(ReportFieldEnum.PARAM.getCode().equalsIgnoreCase(columnProperty.getReportField())) {
                // 处理参数
                handlerParamCondition(columnProperty, queryCondition);
            }else{
                // 处理非参数
                handlerNonParamCondition(columnProperty, queryCondition);
            }

            queryConditions.add(queryCondition);
        }

        return queryConditions;
    }

    private QueryReportConditionInfo getBaseQueryCondition(
        ColumnPropertyDTO columnProperty) {
        QueryReportConditionInfo queryCondition = new QueryReportConditionInfo();
        BeanUtils.copyProperties(columnProperty, queryCondition);

        queryCondition.setFieldName(columnProperty.getEnName());
        queryCondition.setFieldType(columnProperty.getShowTypeName());
        queryCondition.setStringValue(columnProperty.getScreeningCondition().getStringValue());
        queryCondition.setValues(columnProperty.getScreeningCondition()
            .getValues().stream().collect(Collectors.toList()));

        return queryCondition;
    }

    private void handlerParamCondition(ColumnPropertyDTO columnProperty,
        QueryReportConditionInfo queryCondition) {

        // 日期筛选器
        if (FieldType.DATETIME.name().equalsIgnoreCase(columnProperty.getShowTypeName())) {
            List<String> times = getDateTime(columnProperty);

            if (CollUtil.isNotEmpty(times)) {
                if (datePicker.equalsIgnoreCase(columnProperty.getScreeningCondition()
                    .getFilterDateType())) {

                    // 参数时间到天
                    String dayTime = times.get(0).split(" ")[0];
                    queryCondition.setStringValue(dayTime);
                    queryCondition.setIsNoNeedAbsoluteTime(true);
                    queryCondition.setScopeFilterType(ScopeFilterTypeEnum.INTERVAL.getCode());
                    queryCondition.setDateGroupType(null);
                }
            }
        }
    }

    private void handlerNonParamCondition(ColumnPropertyDTO columnProperty,
        QueryReportConditionInfo queryCondition) {

        // 日期筛选器
        if(FieldType.DATETIME.name().equalsIgnoreCase(columnProperty.getShowTypeName())){
            queryCondition.setDateGroupType(DateGroupEnum.YMD_HMS.getCode());
            List<String> times = getDateTime(columnProperty);

            if(CollUtil.isNotEmpty(times)){
                queryCondition.setValues(times.stream().collect(Collectors.toList()));
                queryCondition.setIsNoNeedAbsoluteTime(true);
                queryCondition.setScopeFilterType(ScopeFilterTypeEnum.INTERVAL.getCode());
                queryCondition.setDateGroupType(null);
            }

        }
    }

    private List<String> getDateTime(ColumnPropertyDTO conditionDTO) {
        List<DatePickerDTO> dbDateFilters = new ArrayList<>();

        DatePickerConfigDO pickerConfigDO
            = pickerDAOService.select(conditionDTO.getScreeningCondition().getDatePickerId());

        DatePickerDTO datePickerDTO = pickerConfigDO.getDTO();
        BeanUtils.copyProperties(conditionDTO, datePickerDTO);

        dbDateFilters.add(datePickerDTO);

        Map<String, List<String>> uuidDateTimeMap = datePickerUtil.getTimes(dbDateFilters,
            Lists.newArrayList());

        List<String> dateTimeList = uuidDateTimeMap.get(conditionDTO.getUuid());
        if (CollUtil.isNotEmpty(dateTimeList)) {
            return dateTimeList;

        }
        return Lists.newArrayList();
    }

    private ComputeComponentPropertyDTO getComputeField(List<ComputeComponentPropertyDTO> computeColumnList,
        ColumnPropertyDTO filterField){

        for (ComputeComponentPropertyDTO computerField : computeColumnList) {
            if (filterField.getEnName().equalsIgnoreCase(computerField.getEnName())) {
                return computerField;
            }
        }

        return null;
    }

    private DatasetColumnConfigDTO getDataSetField(List<DatasetColumnConfigDTO> datasetFields,
        ColumnPropertyDTO filterField){

        for (DatasetColumnConfigDTO columnConfigDTO : datasetFields) {
            if (filterField.getEnName().equalsIgnoreCase(columnConfigDTO.getEnName())) {
                return columnConfigDTO;
            }
        }

        return null;
    }
}
