package com.bestpay.bigdata.bi.report.correction;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.response.report.ReportWarnConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wybStart
 * @Date: 2025/6/9  19:17
 * @Description:
 */
@RestController
@RequestMapping("/biReport/reportProcess")
@Slf4j
public class UuidController {

    @Resource
    private ReportUuid reportUuid;

    @Resource
    private DataScreenUuid dataScreenUuid;

    @Resource
    private DatasetUuid datasetUuid;

    @Resource
    private DashboardUuid dashboardUuid;

    @GetMapping("/datasetUuid")
    public Response<String> datasetCorrect() {
        datasetUuid.datasetUuid();
        return Response.ok("订正成功");
    }

    @GetMapping("/reportUuid")
    public Response<List<ReportWarnConfigVO>> reportUuid() {
        reportUuid.reportMarketUuid();
        return Response.ok();
    }

    @GetMapping("/reportUuidValid")
    public Response<List<ReportWarnConfigVO>> reportUuidValid() {
        reportUuid.reportMarketUuidValid();
        return Response.ok();
    }


    @GetMapping("/datascreenUuid")
    public Response<List<ReportWarnConfigVO>> datascreenUuid() {
        dataScreenUuid.dataScreenUuid();
        return Response.ok();
    }

    @GetMapping("/dashboardUuid")
    public Response<List<ReportWarnConfigVO>> dashboardUuid() {
        dashboardUuid.dashboardUuid();
        return Response.ok();
    }

    @GetMapping("/dashboardUuidValid")
    public Response<List<ReportWarnConfigVO>> dashboardUuidValid() {
        dashboardUuid.dashboardUuidValid();
        return Response.ok();
    }

    @GetMapping("/datascreenUuidValid")
    public Response<List<ReportWarnConfigVO>> datascreenUuidValid() {
        dataScreenUuid.dataScreenUuidValid();
        return Response.ok();
    }
}
