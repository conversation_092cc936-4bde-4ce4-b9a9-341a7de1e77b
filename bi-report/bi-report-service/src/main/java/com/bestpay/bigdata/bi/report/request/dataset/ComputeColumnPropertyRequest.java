package com.bestpay.bigdata.bi.report.request.dataset;

import com.bestpay.bigdata.bi.common.dto.report.AdvancedComputing;
import com.bestpay.bigdata.bi.common.dto.report.HighFunction;
import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "计算字段属性信息")
public class ComputeColumnPropertyRequest {

  @ApiModelProperty(value = "字段id")
  private Long id;

  @ApiModelProperty(value = "数据集id")
  private Long datasetId;

  @ApiModelProperty(value = "父级字段id")
  private String parentId;

  @ApiModelProperty(value = "唯一标识")
  private String uuid;

  @ApiModelProperty(value = "中文-字段名称")
  private String name;

  @ApiModelProperty(value = "中文-字段原名")
  private String originalName;

  @ApiModelProperty(value = "英文-字段原名")
  private String originEnName;

  @ApiModelProperty(value = "底层表字段类型")
  private String typeName;

  @ApiModelProperty(value = "字段昵称")
  private String nickName;

  @ApiModelProperty(value = "字段英文名称")
  private String enName;

  @ApiModelProperty(value = "指标名称")
  private String indexName;

  @ApiModelProperty(value = "字段显示类型名称")
  private String showTypeName;

  @ApiModelProperty(value = "计算逻辑")
  private String calculateLogic;

  @ApiModelProperty(value = "聚合方式")
  private String polymerization;

  @ApiModelProperty(value = "计算字段计算逻辑")
  private String fun;

  @ApiModelProperty(value = "类型名称",notes = "数据集字段：指标-index；字段-field；叠加指标-overlayIndex；")
  private String datasetField;

  /**
   * 1.维度时：为字段名称
   * 2.指标时：需带上计算逻辑
   */
  @ApiModelProperty(value = "字段英文名称",required = true)
  private String fieldName;

  @ApiModelProperty(value = "范围过滤类型",required = true,allowableValues = ScopeFilterTypeEnum.ALL_VALUE)
  private String scopeFilterType;

  @ApiModelProperty(value = "比较符用到的常量值",required = true)
  private List<Object> valuesStr;

  @ApiModelProperty(value = "高级计算")
  private AdvancedComputing advancedComputing;

  @ApiModelProperty(value = "日期类型维度统计类型,1-日；2-月；3-年；4-周；5-季度")
  private Integer dateGroupType;

  /**日期截取展示枚举 @DateShowFormatEnum */
  private Integer dateShowFormat;

  @ApiModelProperty(value = "是否统计小计")
  private Boolean showSubtotal=false;

  @ApiModelProperty(value = "数据格式中的度量单位数据类型")
  private String dataType;

  @ApiModelProperty(value = "数据格式中的度量单位小数位数")
  private Integer decimaCarry;

  @ApiModelProperty(value = "数据格式中的度量单位")
  private Integer unit;

  @ApiModelProperty(value = "数据格式中的是否显示千分位")
  private Boolean showThousandth;

  @ApiModelProperty(value = "是否是计算字段")
  private boolean isComputeField;

  @ApiModelProperty(value = "计算字段解析后的逻辑")
  private String computeFieldLogic;

  private List<HighFunction> highFunctions;

  @ApiModelProperty(value = "区间 1动态时间 2固定时间")
  private String intervalType;

  // Report Field Enum
  @ApiModelProperty(value = "字段类型")
  private String fieldType;

  /** */
  @ApiModelProperty(value = "如果选择了动态时间,这里为动态时间 见FilterDateValueEnum 定义 ")
  private String dynamicDate;

  @ApiModelProperty(value = "屏幕条件")
  private String screeningCondition;

  @ApiModelProperty(value = "比较符用到的常量值，当fieldType是SELECT-INPUT字符时，这个字段有值，逗号隔开")
  private String stringValue;

  @ApiModelProperty(value = "值")
  private List<String> valueList;


  @ApiModelProperty(value = "状态码")
  private Integer statusCode;

  @ApiModelProperty(value = "字段")
  private String reportField;

}
