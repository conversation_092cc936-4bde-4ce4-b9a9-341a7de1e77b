package com.bestpay.bigdata.bi.report.controller.common;

import cn.hutool.core.date.StopWatch;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.service.common.RegressionCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/biReport/regression/check/")
@Api(value = "系统回归接口控制器", tags = "系统回归接口控制器")
public class RegressionCheckController {

    @Resource
    private RegressionCheckService regressionCheckService;


    @PostMapping("/regression/{branch}")
    @ApiOperation(httpMethod = "POST", value = "回归接口", notes = "回归接口")
    public Response<String>  regression(@PathVariable String branch, HttpServletRequest httpServletRequest) {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int regression = regressionCheckService.regression(branch, httpServletRequest);
        stopWatch.stop();
        log.info("回归完毕，本次回归共耗时：{}秒",stopWatch.getTotalTimeSeconds());
        return Response.ok("回归完毕:"+regression+ "条数据存在问题");
    }
}
