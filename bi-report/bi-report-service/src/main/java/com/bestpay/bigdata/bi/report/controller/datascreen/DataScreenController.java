package com.bestpay.bigdata.bi.report.controller.datascreen;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.DataScreenVersionTypeEnum;
import com.bestpay.bigdata.bi.common.error.DataScreenErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.datascreen.DataScreenDaoService;
import com.bestpay.bigdata.bi.database.dao.datascreen.DataScreenDO;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenPartakeRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenQueryRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenSaveRequest;
import com.bestpay.bigdata.bi.report.response.datascreen.DataScreenVO;
import com.bestpay.bigdata.bi.report.service.datascreen.DataScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024-08-27
 */
@Slf4j
@RestController
@RequestMapping("/biReport/datascreen")
@Api(value = "数据大屏", tags = "数据大屏")
public class DataScreenController {

    @Resource
    private DataScreenService dataScreenService;


    @Resource
    private DataScreenDaoService dataScreenDaoService;
    /**
     * 数据大屏保存
     *
     * @param request
     * @return
     */
    @PostMapping("/save")
    @ApiOperation(httpMethod = "POST", value = "数据大屏保存", notes = "数据大屏保存")
    public Response<Object> saveDataScreen(@RequestBody @Validated DataScreenSaveRequest request) {
        //在编辑页面的保存。 根据uuid去查询 如果有 则 说明是编辑，则验证责任人。 如果没有 则说明是新增，无需验证
        verifyDSPermissions(request.getDataScreenUUID());

        dataScreenService.save(request);
        DataScreenQueryRequest dataScreenQueryRequest = new DataScreenQueryRequest();
        dataScreenQueryRequest.setDataScreenUUID(request.getDataScreenUUID());
        dataScreenQueryRequest.setVersionType(DataScreenVersionTypeEnum.DRAFT.getCode());
        DataScreenVO dataScreenVO = dataScreenService.queryDataScreenInfo(dataScreenQueryRequest);
        return Response.ok(dataScreenVO);

    }

    private void verifyDSPermissions( String dataScreenUUID) {
        // 能进入到这个controller 要么是 编辑权限 要么是管理员
        if (UserContextUtil.getUserInfo().getIsManager()){
            return;
        }
        List<DataScreenDO> byDataScreenInfo = dataScreenDaoService.queryByDsUuidList(Stream.of(dataScreenUUID).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(byDataScreenInfo)){
            // 说明是编辑权限 需要校验责任人
            UserInfo userInfo = UserContextUtil.get();
            if (!Objects.equals(userInfo.getEmail(),byDataScreenInfo.get(0).getOwnerEmail())){
                throw new BiException(DataScreenErrorCode.DATA_SCREEN_PERMISSIONS,"您暂无此操作权限");
            }
        }
    }

    /**
     * 数据大屏发布
     *
     * @param request
     * @return
     */
    @PostMapping("/publish")
    @ApiOperation(httpMethod = "POST", value = "数据大屏发布", notes = "数据大屏发布")
    public Response<Object> publishDataScreen(@RequestBody @Validated DataScreenSaveRequest request) {
        verifyDSPermissions(request.getDataScreenUUID());
        dataScreenService.publish(request);

        DataScreenQueryRequest dataScreenQueryRequest = new DataScreenQueryRequest();
        dataScreenQueryRequest.setDataScreenUUID(request.getDataScreenUUID());
        dataScreenQueryRequest.setVersionType(DataScreenVersionTypeEnum.PUBLISHED.getCode());
        DataScreenVO dataScreenVO = dataScreenService.queryDataScreenInfo(dataScreenQueryRequest);
        return Response.ok(dataScreenVO);

    }

    /**
     * 数据大屏信息查询
     *
     * @param request
     * @return
     */
    @PostMapping("/queryDataScreenInfo")
    @ApiOperation(httpMethod = "POST", value = "数据大屏信息查询", notes = "数据大屏信息查询")
    public Response<DataScreenVO> queryDataScreenInfo(@RequestBody DataScreenQueryRequest request) {
        DataScreenVO dataScreenVO = dataScreenService.queryDataScreenInfo(request);
        return Response.ok(dataScreenVO);
    }


    /**
     * 数据大屏分享
     *
     * @param request
     * @return
     */
    @PostMapping("/share")
    @ApiOperation(httpMethod = "POST", value = "数据大屏分享", notes = "数据大屏分享")
    public Response<JSONObject> shareDataScreen(@RequestBody DataScreenPartakeRequest request) {
        log.debug("shareDataScreen:{}", JSONUtil.toJsonStr(request));
        verifyDSPermissions(request.getDataScreenUUID());
        String shareUrl = dataScreenService.shareDataScreen(request);


        DataScreenQueryRequest dataScreenQueryRequest = new DataScreenQueryRequest();
        dataScreenQueryRequest.setDataScreenUUID(request.getDataScreenUUID());
        dataScreenQueryRequest.setVersionType(DataScreenVersionTypeEnum.PUBLISHED.getCode());
        DataScreenVO dataScreenVO = dataScreenService.queryDataScreenInfo(dataScreenQueryRequest);

        JSONObject ob = new JSONObject();
        ob.put("shareUrl", shareUrl);
        ob.put("dataScreenInfo", dataScreenVO);
        return Response.ok(ob);
    }


}
