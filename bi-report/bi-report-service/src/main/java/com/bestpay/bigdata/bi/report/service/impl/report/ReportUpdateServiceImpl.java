package com.bestpay.bigdata.bi.report.service.impl.report;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.EMAIL;
import static java.util.Objects.requireNonNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.constant.BIConstant;
import com.bestpay.bigdata.bi.common.dto.dataset.DataSet;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetQueryDTO;
import com.bestpay.bigdata.bi.common.dto.report.ReportSimpleColumn;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.report.component.CommonComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ConditionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.OrderComponentDTO;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.DataSetFieldTypeEnum;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.ReportResourceTypeEnum;
import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.error.ReportErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.dataset.DatasetDAOService;
import com.bestpay.bigdata.bi.database.api.report.ReportDirectoryDAOService;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnDAOService;
import com.bestpay.bigdata.bi.database.api.report.component.NewReportService;
import com.bestpay.bigdata.bi.database.bean.AuthOperateEnum;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetDo;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.database.dao.report.ReportDirectoryDo;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnDo;
import com.bestpay.bigdata.bi.report.enums.common.WarnSourceTypeEnum;
import com.bestpay.bigdata.bi.report.enums.dataset.DatasetStatusEnum;
import com.bestpay.bigdata.bi.report.enums.report.FieldDisplayTypeEnum;
import com.bestpay.bigdata.bi.report.enums.report.ReportFieldEnum;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportCopyRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportStatusUpdateRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportUuidRequest;
import com.bestpay.bigdata.bi.report.request.report.UpdateReportRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.service.impl.report.component.ComponentHandlerRegister;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.bestpay.bigdata.bi.report.util.EngineQueryParamCheckUtil;
import com.bestpay.bigdata.bi.report.util.ParamCheckUtil;
import com.bestpay.bigdata.bi.report.util.ReportDateUtil;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportUpdateServiceImpl implements ReportUpdateService {

  @Resource
  private AiplusService aiplusService;
  @Resource
  private ReportWarnDAOService warnService;
  @Resource
  private DatasetDAOService datasetDAOService;
  @Resource
  private NewReportService newReportService;
  @Resource
  private ComponentHandlerRegister componentHandlerRegister;
  @Resource
  private DatasetService datasetService;
  @Resource
  private AiPlusUserService userService;
  @Resource
  private ReportDirectoryDAOService directoryService;
  @Resource
  private AuthorityCheckUtil authorityCheckUtil;
  @Resource
  private EngineQueryParamCheckUtil engineQueryParamCheckUtil;
  @Resource
  private NewReportService reportService;

  @Resource
  private ReportDateUtil reportDateUtil;
  @Resource
  private ParamCheckUtil paramCheckUtil;


  @Override
  public String generateReportUuid(ReportUuidRequest request) {
    if (request.getIsMeasure() != null && request.getIsMeasure()) {
      return ReportUuidGenerateUtil.generateReportMeasureUuid();
    }

    // 参数校验：确保isConfig和isCompute有且仅有一个为true
    if (request.getIsConfig() == null || request.getIsCompute() == null) {
      throw new BusinessException("参数配置错误，isConfig或isCompute不能为空");
    }
    if (request.getIsConfig().equals(request.getIsCompute())) {
      throw new BusinessException("参数配置错误，isConfig和isCompute必须有且仅有一个为true");
    }

    // 根据请求类型生成不同格式的UUID
    if (Boolean.TRUE.equals(request.getIsConfig())) {
      return ReportUuidGenerateUtil.generateReportConfigUuid();
    } else {
      return ReportUuidGenerateUtil.generateReportComputeUuid();
    }
  }

  @Override
  public Response copy(ReportCopyRequest request) {
    /**
     * 校验
     */
    // 权限校验
    ObjectAuthRequest authRequest = new ObjectAuthRequest();
    authRequest.setAuthResourceId(request.getReportId()+"");
    authRequest.setAuthResourceType(AuthResourceTypeEnum.report.name());
    authorityCheckUtil.checkOwnerAndAuth(authRequest);

    List<NewReportDO> reportList = newReportService.queryExitData(request.getReportName(), null);
    if (reportList == null || reportList.size() > 0) {
      return Response.error(CodeEnum.REPORT_NAME_EXITING);
    }

    /**
     * 获取原始报表核心信息
     */
    NewReportDO report = newReportService.getReportById(request.getReportId());

    /**
     * 重新生成其他配置项目uuid, 只针对报表里面建的计算字段, 当然其他字段的uuid也会存储在map里面
     */
    Map<String, String> computerUuidMap = Maps.newHashMap();


    /**
     * 需要重新生成config uuid
     * 用于找到自定义配置列宽的config uuid
     */
    Map<String, String> configUuidMap = Maps.newHashMap();


    /**
     * 插入主表
     */
    Long newReportId = insertMain(request, report, computerUuidMap);

    /**
     * 获取报表详细信息
     */
    ReportDetailVO detailVO = getReportDetailVO(request, report);

    /**
     * 插入子表：报表详细配置信息
     */
    insertDetailTable(computerUuidMap, newReportId, detailVO, configUuidMap);
    return Response.ok();
  }

  private void insertDetailTable(Map<String, String> computerUuidMap, Long newReportId,
      ReportDetailVO detailVO, Map<String, String> configUuidMap) {
    UpdateReportRequest copyRequest
        = JSONUtil.toBean(JSONUtil.toJsonStr(detailVO), UpdateReportRequest.class);

    // 重新生成uuid
    setUuid(computerUuidMap, copyRequest, configUuidMap);

    copyRequest.setResourceType(ReportResourceTypeEnum.REPORT.name());
    componentHandlerRegister.store(copyRequest, newReportId, true);
  }

  private static void setUuid(Map<String, String> computerUuidMap,
      UpdateReportRequest copyRequest, Map<String, String> configUuidMap) {
    // 计算字段
    ComputeComponentPropertyDTO.setUuid(copyRequest.getComputeColumnList(), computerUuidMap, configUuidMap);
    // 维度
    ComputeComponentPropertyDTO.setUuid(copyRequest.getShowColumnList(), computerUuidMap, configUuidMap);
    // 指标
    ComputeComponentPropertyDTO.setUuid(copyRequest.getIndexColumnList(), computerUuidMap, configUuidMap);
    // 对比
    ComputeComponentPropertyDTO.setUuid(copyRequest.getContrastColumnList(), computerUuidMap, configUuidMap);
    // 筛选
    ComputeComponentPropertyDTO.setUuid(copyRequest.getConditionList(), computerUuidMap, configUuidMap);
    // 过滤
    ComputeComponentPropertyDTO.setUuid(copyRequest.getFilterColumnList(), computerUuidMap, configUuidMap);
    // 排序
    OrderComponentDTO.setOrderUuid(copyRequest.getOrderColumnList(), computerUuidMap, configUuidMap);
    // 关键字
    ComputeComponentPropertyDTO.setUuid(copyRequest.getKeywordList(), computerUuidMap, configUuidMap);
    // 自定义宽度
    TableConfiguration.setUuid(copyRequest.getTableConfigurationObj(), computerUuidMap, configUuidMap);
    // 敏感字段
    UpdateReportRequest.setUuid(copyRequest, computerUuidMap);
  }

  private ReportDetailVO getReportDetailVO(ReportCopyRequest request, NewReportDO report) {
    // 获取其他组件信息
    ReportDetailVO detailVO = new ReportDetailVO();
    BeanUtils.copyProperties(report, detailVO);
    // 层级关系信息
    detailVO.setReportStructureList(JSONUtil
        .toList(report.getReportStructure(), ReportSimpleColumn.class));
    // 数据集信息
    List<DataSet> dataSets
        = datasetService.getDataSet(Lists.newArrayList(report.getDatasetId()));
    detailVO.setDatasetInfoList(JSONUtil.toList(JSONUtil.toJsonStr(dataSets),DatasetInfo.class));
    // 报表市场报表
    detailVO.setResourceType(ReportResourceTypeEnum.REPORT.name());

    // 其他信息
    componentHandlerRegister.query(detailVO, request.getReportId());
    detailVO.setId(request.getReportId());
    return detailVO;
  }

  private Long insertMain(ReportCopyRequest request, NewReportDO report,
      Map<String, String> computerUuidMap) {
    // 责任人为当前用户
    report.setId(null);
    report.setReportName(request.getReportName());
    report.setEmail(UserContextUtil.getUserInfo().getEmail());
    report.setOwnerName(UserContextUtil.getUserInfo().getAccount());
    report.setOwnerNameCh(UserContextUtil.getUserInfo().getNickName());

    // 创建人/更新人
    report.setCreatedAt(new Date());
    report.setUpdatedAt(new Date());
    report.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
    report.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());

    // 存放目录
    report.setDirId(request.getDirId());

    // 重新生成reportStructureUuid 里面的uuid
    setReportStructureUuid(report, computerUuidMap);

    // 重生成 sensitiveFields
    setSensitiveFields(report, computerUuidMap);

    // 插入主表
    return newReportService.insertReport(report);
  }

  private void setReportStructureUuid(NewReportDO report, Map<String, String> computerUuidMap) {
    // 层级关系里面uuid重新生成
    List<ReportSimpleColumn> reportStructureList
        = JSONUtil.toList(report.getReportStructure(), ReportSimpleColumn.class);

    ReportSimpleColumn
        .setColumnUuid(reportStructureList, computerUuidMap);

    report.setReportStructure(JSONUtil.toJsonStr(reportStructureList));
  }


  private void setSensitiveFields(NewReportDO report, Map<String, String> computerUuidMap) {
    if (StringUtils.isNoneBlank(report.getSensitiveFields())) {
      JSONArray sensitiveFieldList = JSON.parseArray(report.getSensitiveFields());

      for (Object object : sensitiveFieldList) {
        String oldUuid = (String) ((JSONObject) object).get("uuid");
        String newUuid = computerUuidMap.get(oldUuid);
        if (StringUtils.isBlank(newUuid)) {
          throw new RuntimeException("敏感字段存在数据异常");
        }
        ((JSONObject) object).put("uuid", newUuid);
        ((JSONObject) object).put("configUuid", ReportUuidGenerateUtil.generateReportConfigUuid());
      }
      report.setSensitiveFields(sensitiveFieldList.toJSONString());
    }
  }

  /**
   * 报表详情 --- 校验数据权限
   * @return
   */
  @Override
  public Response<ReportDetailVO> handlerReportInfo(Long id, NewReportDO reportDo) {
    ReportDetailVO detailVO = getReportDetailVO(reportDo);
    return Response.ok(detailVO);
  }

  /**
   * 报表详情
   * @param id 报表模板类
   * @return
   */
  @Override
  public Response<ReportDetailVO> queryReportTemplate(Long id) {
    NewReportDO reportDo = newReportService.getReportById(id);
    ReportDetailVO detailVO = getReportDetailVO(reportDo);
    return Response.ok(detailVO);
  }

  @Override
  public Report queryById(Long id) {
    Report report = new Report();
    NewReportDO reportDo = newReportService.getReportById(id);
    if (Objects.isNull(reportDo)){
      return null;
    }
    ReportDetailVO detailVO = getReportDetailVO(reportDo);
    BeanUtils.copyProperties(detailVO, report);
    report.setStatusCode(reportDo.getStatusCode());
    report.setOrderColumn(JSONUtil.toJsonStr(detailVO.getOrderColumnList()));
    report.setComputeColumn(JSONUtil.toJsonStr(detailVO.getComputeColumnList()));
    report.setReportStructure(JSONUtil.toJsonStr(detailVO.getReportStructureList()));
    report.setTableConfiguration(JSONUtil.toJsonStr(detailVO.getTableConfigurationObj()));
    report.setContrastColumn(JSONUtil.toJsonStr(detailVO.getContrastColumnList()));
    report.setChartField(JSONUtil.toJsonStr(detailVO.getChartFieldObj()));
    report.setShowIndexTotal(JSONUtil.toJsonStr(detailVO.getShowIndexTotalObj()));
    report.setShowColumn(JSONUtil.toJsonStr(detailVO.getShowColumnList()));
    report.setIndexColumn(JSONUtil.toJsonStr(detailVO.getIndexColumnList()));
    report.setFilterColumn(JSONUtil.toJsonStr(detailVO.getFilterColumnList()));
    report.setCondition(JSONUtil.toJsonStr(detailVO.getConditionList()));
    report.setKeyword(JSONUtil.toJsonStr(detailVO.getKeywordList()));
    report.setDatasetInfo(JSONUtil.toJsonStr(detailVO.getDatasetInfoList()));
    return report;
  }

  @Override
  @Transactional(rollbackFor = Throwable.class)
  public Response<Object> insertReportTemplate(UpdateReportRequest request, boolean ischeck) {
    // 校验报表信息
    if(ischeck) {
      checkReportParam(request);
    }

    // 校验名称 report_name 是否重复
    List<NewReportDO> reportList = newReportService.queryExitData(request.getReportName(),
        request.getId());
    if (reportList == null || reportList.size() > 0) {
      return Response.error(CodeEnum.REPORT_NAME_EXITING);
    }

    // main info
    NewReportDO main = new NewReportDO();
    BeanUtils.copyProperties(request, main);
    main.setCreatedAt(request.getCreatedAt()==null?new Date():request.getCreatedAt());
    main.setUpdatedAt(request.getUpdatedAt()==null?new Date():request.getUpdatedAt());
    main.setCreatedBy(request.getCreatedBy()==null?UserContextUtil.getUserInfo().getEmail():request.getCreatedBy());
    main.setUpdatedBy(request.getUpdatedBy()==null?UserContextUtil.getUserInfo().getEmail():request.getUpdatedBy());
    main.setStatusCode(request.getStatusCode()==null?StatusCodeEnum.OFFLINE.getCode():request.getStatusCode());
    main.setDatasetId(CollUtil.isEmpty(request.getDatasetInfoList()) ? null
        : request.getDatasetInfoList().get(0).getDatasetId());
    main.setReportStructure(JSONUtil
        .toJsonStr(request.getReportStructureList()));// 报表结构

    // 责任人
    UserInfo user = getUserInfo(request.getEmail());
    main.setOwnerName(user!=null?user.getAccount():"unknown");
    main.setOwnerNameCh(user!=null?user.getNickName():"unknown");

    // set dirId and orderNum
    requireNonNull(request.getDirId(), "dirId can not be null");
    Long maxDirSort = newReportService.getMaxDirSort();
    Long next = Optional.ofNullable(maxDirSort).orElse(0L) + 1;
    main.setDirId(request.getDirId());
    main.setOrderNum(next);

    log.info("insertReportTemplate start");
    Long reportId = newReportService.insertReport(main);

    // 保存 report 其他组件
    request.setResourceType(ReportResourceTypeEnum.REPORT.name());
    componentHandlerRegister.store(request, request.getId()!=null ? request.getId():reportId,false);

    return Response.ok(reportId);
  }

  @Override
  @Transactional(rollbackFor = Throwable.class)
  public Response<Object> updateReportTemplate(UpdateReportRequest request) {

    //修改报表不应该修改报表状态，设置为null为不更新此字段，from AILAB-34257
    request.setStatusCode(null);

    // updateDTO
    NewReportDO report = new NewReportDO();
    BeanUtils.copyProperties(request, report);
    report.setUpdatedAt(new Date());
    report.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
    report.setDatasetId(request.getDatasetInfoList().get(0).getDatasetId());
    report.setReportStructure(JSONUtil
        .toJsonStr(request.getReportStructureList()));// 报表结构

    // 报表名称是否重复
    List<NewReportDO> reportList = newReportService.queryExitData(request.getReportName(),
        request.getId());

    if (reportList == null || reportList.size() > 0) {
      return Response.error(CodeEnum.REPORT_NAME_EXITING);
    }

    // 校验报表信息
    checkReportParam(request);

    // 修改责任人
    String requestEmail = request.getEmail();
    if (StringUtil.isNotEmpty(requestEmail)) {
      report.setEmail(requestEmail);
      UserInfo user = aiplusService.getUserInfoByEmail(requestEmail);
      report.setOwnerName(user.getAccount());
      report.setOwnerNameCh(user.getNickName());
    }


    log.info("insertReportTemplate start");
    newReportService.updateReport(report);

    // 保存 report 其他组件
    componentHandlerRegister.delete(request.getId(), ReportResourceTypeEnum.REPORT.getCode(),null);
    request.setResourceType(ReportResourceTypeEnum.REPORT.name());
    componentHandlerRegister.store(request, request.getId(),false);

    return Response.ok(request.getId());
  }

  /**
   * 报表的操作状态
   * @param report
   * @return
   */
  @Transactional(rollbackFor = Throwable.class)
  @Override
  public Response execReportOperateStatus(ReportStatusUpdateRequest report) {
    if(StatusCodeEnum.DELETE.getCode()==report.getStatusCode()) {
      // 权限校验 -- 仅限制责任人
      NewReportDO dbReport = newReportService.getReportById(report.getId());
      authorityCheckUtil.checkOwner(EMAIL, dbReport.getEmail());
    }else {
      // 权限校验 （仅限责任人 |  授权编辑）
      ObjectAuthRequest request = new ObjectAuthRequest();
      request.setAuthResourceId(report.getId()+"");
      request.setAuthResourceType(AuthResourceTypeEnum.report.name());
      request.setAuthOperate(AuthOperateEnum.edit.name());
      authorityCheckUtil.checkOwnerAndAuth(request);
    }

    NewReportDO update = new NewReportDO();
    update.setId(report.getId());
    update.setStatusCode(report.getStatusCode());
    update.setUpdatedAt(DateUtil.date());
    update.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
    newReportService.updateReport(update);

    if (StatusCodeEnum.DELETE.getCode() == report.getStatusCode()
        || StatusCodeEnum.OFFLINE.getCode() == report.getStatusCode()) {
      ReportWarnDo warnDo = new ReportWarnDo();
      warnDo.setReportId(report.getId()+"");
      warnDo.setWarnSourceType(WarnSourceTypeEnum.REPORT.getCode());
      warnDo.setStatusCode(report.getStatusCode());
      warnDo.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
      warnService.update(warnDo);
    }
    // 如果是删除report，则删除report下的所有组件
    if (StatusCodeEnum.DELETE.getCode() == report.getStatusCode()) {
      componentHandlerRegister.delete(report.getId(), ReportResourceTypeEnum.REPORT.getCode(),null);
    }
    return Response.ok();
  }

  private ReportDetailVO getReportDetailVO(NewReportDO reportDo) {
    // 报表主表信息，组件信息
    ReportDetailVO detailVO = new ReportDetailVO();
    BeanUtils.copyProperties(reportDo, detailVO);
    detailVO.setReportStructureList(JSONUtil
        .toList(reportDo.getReportStructure(), ReportSimpleColumn.class));
    detailVO.setDatasetInfoList(getDatasetInfos(reportDo));
    detailVO.setResourceType(ReportResourceTypeEnum.REPORT.name());
    componentHandlerRegister.query(detailVO, reportDo.getId());
    detailVO.setId(reportDo.getId());

    // 组织信息
    List<Org> orgList = aiplusService.getOrgList();
    for (Org org : orgList) {
      if (StrUtil.equals(detailVO.getOrgCode(), org.getCode())) {
        detailVO.setOrgName(org.getName());
      }
    }

    // 用户信息
    UserInfo user = getUserInfo(detailVO.getEmail());
    detailVO.setOwnerNameCh(user!=null ? user.getNickName() : "unknown");

    // 目录名称
    ReportDirectoryDo reportDirectoryDo = directoryService.queryDbById(reportDo.getDirId());
    detailVO.setDirName(reportDirectoryDo.getName());

    // 以主表的时间为准
    detailVO.setCreatedAt(reportDo.getCreatedAt());
    detailVO.setCreatedBy(reportDo.getCreatedBy());
    detailVO.setUpdatedAt(reportDo.getUpdatedAt());
    detailVO.setUpdatedBy(reportDo.getUpdatedBy());

    return detailVO;
  }

  private List<DatasetInfo> getDatasetInfos(NewReportDO reportDo) {
    List<DataSet> dbDataSets = datasetService.getDataSet(
        Lists.newArrayList(reportDo.getDatasetId()));

    List<DatasetInfo> datasetInfoList = Lists.newArrayList();
    for (DataSet dbDataSet : dbDataSets) {
      DatasetInfo datasetInfo = new DatasetInfo();
      BeanUtils.copyProperties(dbDataSet, datasetInfo);
      datasetInfoList.add(datasetInfo);
    }
    return datasetInfoList;
  }

  private void checkReportParam(UpdateReportRequest request){
    if(CollUtil.isEmpty(request.getIndexColumnList())
        && CollUtil.isEmpty(request.getShowColumnList())){
      throw new BiException(DashboardErrorCode.DASHBOARD_MUST_CONF_DIMENSION_OR_MEASURE,"必须配置维度或者度量");
    }

    // 数值类型过滤必填范围过滤类型
    if(CollUtil.isNotEmpty(request.getFilterColumnList())){
      for (FilterComponentPropertyDTO filterCondition : request.getFilterColumnList()) {
        if(FieldType.DECIMAL.name().equalsIgnoreCase(filterCondition.getFieldType())){
          ScopeFilterTypeEnum.getName(filterCondition.getScopeFilterType());
        }
      }
    }

    // 参数筛选器必须配置默认值
    if(CollUtil.isNotEmpty(request.getConditionList())){
      for (ConditionComponentPropertyDTO columnProperty : request.getConditionList()) {
        if(ReportFieldEnum.PARAM.getCode().equals(columnProperty.getReportField())){
          if(isParamWithoutDefaultValue(columnProperty)){
            throw new BiException(ReportErrorCode.REPORT_FILTER_MUST_HAVE_DEFAULT,"报表参数筛选器必须配置默认值");
          }
        }
      }
    }

    // 校验数据集信息
    checkDataSet(request);

    Long datasetId = request.getDatasetInfoList().get(0).getDatasetId();
    List<DatasetColumnConfigDTO> columnConfigList = engineQueryParamCheckUtil.getColumnConfigList(datasetId);
    Map<String, DatasetColumnConfigDTO> columnEnNameMap = columnConfigList.stream().collect(Collectors.toMap(DatasetColumnConfigDTO::getEnName, Function.identity()));
    Map<String, DatasetColumnConfigDTO> columnOriginEnNameMap = columnConfigList.stream().filter(c->StrUtil.isNotBlank(c.getOriginEnName())).collect(Collectors.toMap(DatasetColumnConfigDTO::getOriginEnName, Function.identity()));

    for (ComputeComponentPropertyDTO columnProperty : request.getComputeColumnList()) {
      String computeEnName = columnProperty.getEnName();
      DatasetColumnConfigDTO dto = new DatasetColumnConfigDTO();
      BeanUtils.copyProperties(columnProperty, dto);
      dto.setEnName(computeEnName);
      dto.setShowTypeName(columnProperty.getShowTypeName());
      String fieldType = columnProperty.getFieldType();
      DataSetFieldTypeEnum dataSetFieldTypeEnum = DataSetFieldTypeEnum.getNameByCode(fieldType);
      dto.setFieldType(dataSetFieldTypeEnum);
      paramCheckUtil.checkFun(columnEnNameMap,computeEnName,columnProperty.getFun());
      columnEnNameMap.put(columnProperty.getEnName(),dto);
    }

    Boolean check;
    Long id = request.getId();
    if (Objects.isNull(id)){
      check = Boolean.TRUE;
    }else {
      NewReportDO report = reportService.getReportById(id);
      if(report!=null) {
        check = reportDateUtil.isCheck(report.getCreatedAt());
      }else {
        check=false;
      }
    }
    if (check){

      //校验展示字段 是否出现改动
      checkColumnPropertyList(request.getShowColumnList(),
          columnEnNameMap,
          "展示字段",
          columnOriginEnNameMap);

      // 校验对比
      checkColumnPropertyList(request.getContrastColumnList()
              .stream().filter(p->!p.getEnName().equals("measureName")).collect(Collectors.toList()),
          columnEnNameMap,
          "对比字段",
          columnOriginEnNameMap);

      // 校验指标
      checkColumnPropertyList(request.getIndexColumnList(),
          columnEnNameMap,
          "指标字段",
          columnOriginEnNameMap);

      // 校验关键字
      checkColumnPropertyList(request.getKeywordList(),
          columnEnNameMap,
          "关键字字段",
          columnOriginEnNameMap);

      // 校验筛选
      checkColumnPropertyList(request.getConditionList(),
          columnEnNameMap,
          "筛选字段",
          columnOriginEnNameMap);

      // 校验过滤
      checkColumnPropertyList(request.getFilterColumnList(),
          columnEnNameMap,
          "过滤字段",
          columnOriginEnNameMap);
    }
  }

  private static <T extends CommonComponentPropertyDTO> void checkColumnPropertyList(List<T> columnList,
      Map<String, DatasetColumnConfigDTO> columnEnNameMap,
      String source,
      Map<String, DatasetColumnConfigDTO> columnOriginEnNameMap) {

    if (CollUtil.isNotEmpty(columnList)){
      for (T columnProperty : columnList) {
        String enName = columnProperty.getEnName();
        if (StrUtil.isBlank(enName) || enName.equals("measureName")){
          continue;
        }
        DatasetColumnConfigDTO columnConfigDTO = columnEnNameMap.get(enName);
        if (Objects.isNull(columnConfigDTO)){
          columnConfigDTO = columnOriginEnNameMap.get(enName);
        }
        checkField(columnConfigDTO,source,enName);
        checkFieldType(columnConfigDTO.getFieldType(),columnProperty.getFieldType(),source,enName);
        checkShowTypeName(columnConfigDTO.getShowTypeName(),columnProperty.getShowTypeName(),source,enName);
      }
    }
  }

  /**
   * 校验字段是否存在
   * @param object 范型
   * @param enName 英文名称
   */
  private static void checkField(Object object,String source,String enName){
    if (Objects.isNull(object)){
      throw new BiException(DashboardErrorCode.DASHBOARD_DEL_FIELD_TIP,String.format(BIConstant.DEL_FIELD_TIP,source, enName));
    }
  }

  /**
   * 校验字段纬度是否改变
   * @param fieldTypeEnum 纬度枚举
   * @param fieldType  fieldTypeEnum
   * @param enName 英文名称
   */
  private static void checkFieldType(DataSetFieldTypeEnum fieldTypeEnum, String fieldType, String source,String enName){
    if (Objects.nonNull(fieldTypeEnum) && StrUtil.isNotBlank(fieldType) && !fieldTypeEnum.name().equals(fieldType) && !fieldTypeEnum.name().equals(DataSetFieldTypeEnum.ALL.name())){
      throw new BiException(DashboardErrorCode.DASHBOARD_MODIFY_FIELD_TIP,String.format(BIConstant.MODIFY_FIELD_TIP,source, enName));
    }
  }

  /**
   *
   * @param showTypeName 字段展示类型
   * @param showTypeName2 字段展示类型
   * @param enName 英文名称
   */
  private static void checkShowTypeName(String showTypeName ,String showTypeName2,String source, String enName){
    if (FieldDisplayTypeEnum.STR_TYPE_LIST.contains(showTypeName) && FieldDisplayTypeEnum.STR_TYPE_LIST.contains(showTypeName2)){
      return;
    }
    if (!showTypeName.equals(showTypeName2)){
      throw new BiException(DashboardErrorCode.DASHBOARD_FIELD_TYPE_TIP,String.format(BIConstant.FIELD_TYPE_TIP,source, enName));
    }
  }




  private static boolean isParamWithoutDefaultValue(ConditionComponentPropertyDTO columnProperty) {
    return columnProperty.getScreeningCondition() == null
        ||
        (StringUtil.isEmpty(columnProperty.getScreeningCondition().getStringValue())
            && CollUtil.isEmpty(columnProperty.getScreeningCondition().getDefaultValues()));
  }

  private void checkDataSet(UpdateReportRequest request) {
    if (CollUtil.isEmpty(request.getDatasetInfoList())) {
      throw new BiException(ReportErrorCode.REPORT_DATASET_EMPTY,"数据集信息为空");
    }

    DatasetQueryDTO queryDTO = DatasetQueryDTO.builder()
        .statusCode(DatasetStatusEnum.ONLINE.getCode())
        .idList(Lists.newArrayList(request.getDatasetInfoList().get(0).getDatasetId()))
        .build();

    List<DatasetDo> datasetDoList = datasetDAOService.query(queryDTO);
    if (CollUtil.isEmpty(datasetDoList)) {
      throw new BiException(ReportErrorCode.REPORT_DATASET_STATUS_ERROR,"数据集已下线");
    }

    DatasetDo datasetDo = datasetDoList.get(0);
    authorityCheckUtil.checkAuthOrg(datasetDo.getOrgAuth());

  }

  private UserInfo getUserInfo(String email) {
    AiPlusUserSearchRequest userQuery = new AiPlusUserSearchRequest();
    userQuery.setOwnerNames(Lists.newArrayList(email));
    List<UserInfo> users = userService.getUserList(userQuery);
    if(CollUtil.isEmpty(users)){
      return null;
    }

    return users.get(0);
  }


}
