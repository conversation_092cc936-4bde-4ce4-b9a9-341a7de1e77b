package com.bestpay.bigdata.bi.report.service.report;

import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.report.request.report.ReportCopyRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportStatusUpdateRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportUuidRequest;
import com.bestpay.bigdata.bi.report.request.report.UpdateReportRequest;

/**
 * <AUTHOR>
 */
public interface ReportUpdateService {

  Report queryById(Long id);

  /**
   * 新增报表模板
   * @return Response<Integer>
   */
  Response<Object> insertReportTemplate(UpdateReportRequest request, boolean ischeck);

  /**
   * 编辑报表模板
   *
   * @param report      报表模板类
   * @return Response<Integer>
   */
  Response<Object> updateReportTemplate(UpdateReportRequest report);

  /**
   * 获取报表信息
   * @return
   */
  Response<ReportDetailVO> handlerReportInfo(Long id, NewReportDO reportDo);

  /**
   * 查询报表模板详情
   *
   * @param id 报表模板类
   * @return Response<ReportResponse>
   */
  Response<ReportDetailVO> queryReportTemplate( Long id);

  /**
   * 报表的操作状态
   * @param report
   * @return
   */
  Response execReportOperateStatus(ReportStatusUpdateRequest report);


  /**
   * 用于生成报表配置字段的config uuid 和 创建计算字段的uuid
   * @param report
   * @return config uuid or uuid
   */
  String generateReportUuid(ReportUuidRequest report);

  /**
   * 报表复制
   */
  Response copy(ReportCopyRequest request);
}
