package com.bestpay.bigdata.bi.report.service.impl.report.component;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.COMPUTE_COLUMN_FLAG;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.CommonComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.database.api.report.component.NewReportComputeService;
import com.bestpay.bigdata.bi.database.dao.report.component.NewReportComputeDO;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class CommonHandler {

  @Resource
  private NewReportComputeService computeService;

  @Resource
  private DatasetService datasetService;



  /**
   * 设置原始中文名
   */
  void setOriginalCnName(ReportDetailVO detailVO,
      List<? extends CommonComponentPropertyDTO> columnPropertyList) {

    DatasetColumnConfigRequest configRequest = new DatasetColumnConfigRequest();
    if (CollUtil.isNotEmpty(detailVO.getDatasetInfoList())){
      configRequest.setDatasetId(detailVO.getDatasetInfoList().get(0).getDatasetId());
    }

    // 数据集字段
    List<DatasetColumnConfigDTO> dataSetColumns
        = datasetService.getColumnConfigList(configRequest).getData();

    // 计算字段中文名
    Map<String, String> computerNameMap = new HashMap<>();

    if (Objects.nonNull(detailVO.getId())){
      computerNameMap   = getComputeName(computeService.getById(detailVO.getId()));
    }


    if (CollUtil.isEmpty(columnPropertyList)) {
      return;
    }

    if (CollUtil.isEmpty(dataSetColumns) && MapUtil.isEmpty(computerNameMap)) {
      return;
    }

    // 匹配获取中文名
    for (CommonComponentPropertyDTO columnProperty : columnPropertyList) {
      columnProperty.setOriginalName(dataSetColumns, computerNameMap);
    }

  }

  private Map<String, String> getComputeName(List<NewReportComputeDO> computeDOS) {
    Map<String, String> map = new HashMap<>();
    for (NewReportComputeDO columnProperty : computeDOS) {
      if (StringUtils.isEmpty(columnProperty.getEnName())) {
        columnProperty.setEnName(COMPUTE_COLUMN_FLAG.concat(String.valueOf(Math.abs(columnProperty.hashCode()))));
      }
      map.put(columnProperty.getEnName(), columnProperty.getName());
    }
    return map;
  }
}
