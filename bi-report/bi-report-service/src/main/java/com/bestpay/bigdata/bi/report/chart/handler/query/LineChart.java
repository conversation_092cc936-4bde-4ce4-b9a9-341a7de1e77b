package com.bestpay.bigdata.bi.report.chart.handler.query;

import cn.hutool.core.collection.CollUtil;
import com.bestpay.bigdata.bi.backend.api.QueryService;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.dto.report.component.ContrastComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.entity.ColumnName;
import com.bestpay.bigdata.bi.report.chart.bean.ChartContext;
import com.bestpay.bigdata.bi.report.chart.bean.ChartData;
import com.bestpay.bigdata.bi.report.chart.bean.ChartHeader;
import com.bestpay.bigdata.bi.report.chart.handler.AbstractChart;
import com.bestpay.bigdata.bi.report.enums.report.ReportFieldEnum;
import com.bestpay.bigdata.bi.report.service.common.EngineDataQueryService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * ClassName: LineChart
 * Package: com.bestpay.bigdata.bi.report.chart
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/3/13 9:58
 * @Version 1.0
 */
@Slf4j
public class LineChart extends AbstractChart
{
    public LineChart(ChartContext context,
        QueryService queryService,
        EngineDataQueryService dataQueryService) {

        super(context, queryService,dataQueryService);
    }

    @Override
    public ChartHeader getChartHeader()
    {
        List<ColumnName> columnNames = getColumnNames();
        ChartHeader chartHeader = new ChartHeader();
        chartHeader.setHeader(columnNames);
        return chartHeader;
    }

    @Override
    public ChartData makeChartData(ChartHeader header, List<List<String>> baseData)
    {
        List<ContrastComponentPropertyDTO> contrastColumnList = context.getReportBaseModel().getContrastColumnList();

        List<Map<String, Object>> formatData = null;
        if (contrastColumnList.size() > 1) { // contrast table
            formatData = processContrastLineChart(header);
        } else {
            formatData = getLineChartData(header.getHeader(), baseData);
        }

        ChartData chartData = new ChartData();
        chartData.setData(formatData);
//        chartData.setRowCount(context.getRowCount());
        return chartData;
    }

    /**
     * 维度1个、指标多个、叠加指标多个
     * @param columnNames
     * @param data
     * @return
     */
    private List<Map<String, Object>> getLineChartData(List<ColumnName> columnNames, List<List<String>> data) {
        List<Map<String, Object>> result = new ArrayList<>();
        int m = 0, n = 0, k = 0;
        if (columnNames != null && CollUtil.isNotEmpty(data)) {
            for (int i = 0; i < columnNames.size(); i++) {
                Map<String, Object> map = new HashMap<>(columnNames.size());
                List<String> colList = new ArrayList<>(data.get(0).size());
                for (List<String> datum : data) {
                    colList.add(datum.get(i));
                }
                if (Objects.equals(ReportFieldEnum.INDEX_FIELD.getCode(), columnNames.get(i).getReportField())) {
                    map.put(ReportFieldEnum.INDEX_FIELD.getCode() + (m++), colList);
                }
                if (Objects.equals(ReportFieldEnum.DIM_FIELD.getCode(), columnNames.get(i).getReportField())) {
                    map.put(ReportFieldEnum.DIM_FIELD.getCode() + (n++), colList);
                }
                if (Objects.equals(ReportFieldEnum.OVERLAY_INDEX_FIELD.getCode(), columnNames.get(i).getReportField())) {
                    map.put(ReportFieldEnum.OVERLAY_INDEX_FIELD.getCode() + (k++), colList);
                }
                result.add(map);
            }
        }
        return result;
    }

    @Override
    public ChartTypeEnum getChartType()
    {
        return ChartTypeEnum.LINE_CHART;
    }
}
