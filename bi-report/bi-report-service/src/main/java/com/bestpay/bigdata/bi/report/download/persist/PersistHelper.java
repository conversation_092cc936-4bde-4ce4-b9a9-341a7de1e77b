package com.bestpay.bigdata.bi.report.download.persist;

import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.report.download.file.FileWriteInfo;
import com.bestpay.bigdata.bi.report.download.file.FileWriter;
import com.bestpay.bigdata.bi.report.download.file.FileWriterFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * ClassName: PersistHelper
 * Package: com.bestpay.bigdata.bi.report.download
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/12/12 9:58
 * @Version 1.0
 */
@Slf4j
public abstract class PersistHelper implements persist
{
    private FileWriter fileWriter;

    public PersistHelper(FileType fileType, String localFilePath)
    {
        this.fileWriter = FileWriterFactory.createNewFileWriter(fileType, localFilePath);
    }

    /**
     * you can use it finish all file download
     * @param fileWriteInfo single data page info
     * @param number The number data page
     * @param total total
     */
    @Override
    public void writeToLocalPath(FileWriteInfo fileWriteInfo, int number, int total) {
        try {
            fileWriter.write(fileWriteInfo, number, total);
        } catch (Exception e) {
            log.error("fileWriter writeToLocalPath happen error ", e);
            releaseResources();
        }
    }

    @Override
    public void releaseResources() {
        fileWriter.releaseResources();
    }
}
