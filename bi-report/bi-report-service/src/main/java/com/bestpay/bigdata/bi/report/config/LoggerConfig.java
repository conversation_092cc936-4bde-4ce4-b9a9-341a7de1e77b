package com.bestpay.bigdata.bi.report.config;

import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.logging.LogLevel;
import org.springframework.boot.logging.LoggingSystem;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LoggerConfig {

    private static final Logger logger = LoggerFactory.getLogger(LoggerConfig.class);

    private static final String LOGGER_TAG = "logging.level.";

    @Autowired
    private LoggingSystem loggingSystem;


    @ApolloConfigChangeListener
    private void configChangeListener(ConfigChangeEvent changeEvent) {
        for (String key : changeEvent.changedKeys()) {
            if(StringUtils.containsIgnoreCase(key, LOGGER_TAG)) {
                ConfigChange change = changeEvent.getChange(key);
                String strLevel = change.getNewValue() == null ? change.getOldValue() : change.getNewValue();
                LogLevel level = LogLevel.valueOf(strLevel.toUpperCase());
                loggingSystem.setLogLevel(key.replace(LOGGER_TAG, ""), level);
                logger.info("{}={}", key, strLevel);
            }
        }
    }
}
