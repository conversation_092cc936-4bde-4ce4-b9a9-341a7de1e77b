package com.bestpay.bigdata.bi.report.enums.report;

/**
 * <AUTHOR>
 * @create 2023-05-22-13:52
 */
public enum DateShowFormatEnum {

    YEAR(1),
    YEAR_QUARTER(2),
    QUARTER(3),
    <PERSON><PERSON><PERSON>(4),
    YEAR_MONTH(5),
    YEAR_WEEK(6),
    <PERSON><PERSON><PERSON>(7),
    YEAR_MONTH_DAY(8),
    YMD_HMS(9),
    <PERSON><PERSON><PERSON>(10),
    <PERSON><PERSON><PERSON>(11),
    HOUR_MINUTE(12),
    HMS(13);

    int code;
    DateShowFormatEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
