package com.bestpay.bigdata.bi.report.download.bean;

import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.common.oss.OssService;
import com.bestpay.bigdata.bi.common.oss.OssServiceFactory;
import com.bestpay.bigdata.bi.report.download.DownloadId;
import lombok.Data;

/**
 * ClassName: DownloadContext
 * Package: com.bestpay.bigdata.bi.report.download
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/12/11 15:22
 * @Version 1.0
 */
@Data
public class DownloadContext
{
    private DownloadId downloadId;
    private String requestSystem;
    private String username;
    private String userOrg;
    private String fileIdentifier;
    /**
     * 查询类型（0：多维分析 1：数据探查 2：报表查询）
     */
    private Integer typeCode;
    private FileType fileType;

    private String clusterName;
    private volatile DownloadStatusEnum status;
    private String localFilePath;
    private String remoteFilePath;
    private String compressFilePath;
    private String zipPassword;
    private String errorMessage;

    /** 运行时写入 */
    private String downloadContent;
    private String queryId;

    /**
     * 文件处理工厂
     */
    private OssServiceFactory ossServiceFactory;
}
