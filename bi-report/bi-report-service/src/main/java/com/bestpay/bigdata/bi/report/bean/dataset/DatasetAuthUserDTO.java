package com.bestpay.bigdata.bi.report.bean.dataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("数据集权限用户")
public class DatasetAuthUserDTO {
  @ApiModelProperty("用户|用户组类型")
  private String type;
  @ApiModelProperty("用户|用户组值")
  private String value;
  @ApiModelProperty("用户|用户组名称")
  private String name;
}
