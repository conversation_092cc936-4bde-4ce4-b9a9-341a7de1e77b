package com.bestpay.bigdata.bi.report.sql.bean;

import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import com.bestpay.bigdata.bi.report.sql.enums.CalculationScenarioEnum;
import com.bestpay.bigdata.bi.report.sql.function.FunctionProcessResult;
import com.bestpay.bigdata.bi.report.sql.function.MysqlHighFunctionProcess;
import com.bestpay.bigdata.bi.report.sql.provider.generate.ParseSqlContent;
import com.bestpay.bigdata.bi.report.sql.provider.part.SelectItem;
import com.bestpay.bigdata.bi.report.sql.provider.part.SqlContent;
import com.bestpay.bigdata.bi.report.sql.provider.part.WithPart;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-06-13-17:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SqlResult {

    private String sql;

    private SqlContent sqlContent;

    /** general use to high function */


    /** general use to high function */

    /** Multiple tables use join construct logic layer table */
    private boolean isUseLogicView;

    private String logicView;

    private String replaceTarget;
    /** Multiple tables use join construct logic layer table */

    private SqlContent sqlContentWithNoDateFormat;

    private Map<String, String> uuidAliasMap = new HashMap<>();

    private SQLEngine sqlEngine;

    private ReportSqlInfo sqlInfo;

    private String replaceSql(String sourceSql) {
        if (isUseLogicView) {
            return sourceSql.replace(replaceTarget, logicView);
        }
        return sourceSql;
    }


    public String nestedParseSql(SqlContent targetSqlContent) {

        String sourceSql = "";
        if (sqlContent instanceof WithPart) {
            WithPart withPart = (WithPart) sqlContent;
            WithPart resultWithPart = new WithPart();
            resultWithPart.setSqlContent(targetSqlContent);
            resultWithPart.setWithQueryPartList(withPart.getWithQueryPartList());
            sourceSql = ParseSqlContent.parseSqlContentToSqlStr(resultWithPart);
        } else {
            sourceSql = ParseSqlContent.parseSqlContentToSqlStr(targetSqlContent);
        }
        return replaceSql(sourceSql);
    }

    public SqlContent getSqlContent() {
        if (sqlContent instanceof WithPart) {
            WithPart withPart = (WithPart) sqlContent;
            return withPart.getSqlContent();
        }
        return sqlContent;
    }


    public SqlContent getSqlContentWithNoDateFormat() {
        if (sqlContentWithNoDateFormat instanceof WithPart) {
            WithPart withPart = (WithPart) sqlContentWithNoDateFormat;
            return withPart.getSqlContent();
        }
        return sqlContentWithNoDateFormat;
    }


    public SqlContent getOriginSqlContent() {
        return sqlContent;
    }


    public SqlContent getOriginWithNoDateFormatSqlContent() {
        return sqlContentWithNoDateFormat;
    }


    public List<SelectItem> getOuterSelectItemList() {
        if (sqlContent instanceof WithPart) {
            WithPart withPart = (WithPart) sqlContent;
            return withPart.getSqlContent().getSelectPart().getSelectItemList();
        }
        return sqlContent.getSelectPart().getSelectItemList();
    }
}
