package com.bestpay.bigdata.bi.report.request.subscribe;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "订阅统计")
public class SubCountRequest {

    @ApiModelProperty(value = "订阅对象ID")
    @NotNull(message = "订阅对象ID不能为空")
    private Long objectId;

    @ApiModelProperty(value = "订阅对象类型")
    @NotBlank(message = "订阅对象类型不能为空")
    private String objectType;

}
