package com.bestpay.bigdata.bi.report.correction;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.error.StandardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.database.api.dataset.DatasetCorrectionLogService;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetCorrectionLogDO;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetRowAuthRuleDo;
import com.bestpay.bigdata.bi.database.mapper.dataset.DatasetRowAuthRuleMapper;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;

@Component
@Slf4j
public class DatasetUuid {

    @Resource
    private DatasetService datasetService;

    @Resource
    private DatasetRowAuthRuleMapper authRuleMapper;

    @Resource
    private DatasetCorrectionLogService correctionLogService;

    @Transactional
    public void datasetUuid() {
        // 生成批次ID
        String batchId = UUID.randomUUID().toString();
        List<DatasetCorrectionLogDO> correctionLogs = new ArrayList<>();

        try {
            // 查询所有t_dataset_row_auth_rule表数据
            List<DatasetRowAuthRuleDo> authRuleList = authRuleMapper.queryAll();
            log.info("查询到t_dataset_row_auth_rule数据条数: {}", authRuleList.size());

            // 处理每个权限规则的value字段
            List<DatasetRowAuthRuleDo> updatedRules = new ArrayList<>();

            // 创建数据集字段配置缓存
            Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap = new HashMap<>();
            int processedCount = 0;
            int errorCount = 0;

            for (DatasetRowAuthRuleDo rule : authRuleList) {
                log.info("处理权限规则ID: {}", rule.getId());
                if (StringUtils.isNotBlank(rule.getValue())) {
                    Long datasetId = rule.getDatasetId();
                    ProcessResult result = processRuleValueWithErrorHandling(rule.getValue(), columnConfigMap, datasetId);

                    // 记录所有处理的记录（成功和失败都记录）
                    DatasetCorrectionLogDO correctionLog = buildCorrectionLog(batchId, rule, result);
                    correctionLogs.add(correctionLog);

                    if (result.isSuccess()) {
                        if (!rule.getValue().equals(result.getProcessedValue())) {
                            rule.setValue(result.getProcessedValue());
                            updatedRules.add(rule);
                            processedCount++;
                        }
                    } else {
                        // 记录错误但不影响其他记录的处理
                        errorCount++;
                    }
                }
            }

            // 批量插入
            if (!updatedRules.isEmpty()) {
                authRuleMapper.batchInsert(updatedRules);
                log.info("成功更新{}条权限规则数据", processedCount);
            } else {
                log.info("没有需要更新的权限规则数据");
            }

            // 批量记录订正日志
            if (!correctionLogs.isEmpty()) {
                try {
                    correctionLogService.batchInsert(correctionLogs);
                    log.info("记录了{}条订正日志", correctionLogs.size());
                } catch (Exception e) {
                    log.error("记录订正日志失败", e);
                }
            }

            log.info("UUID数据订正执行完成，批次ID: {}，成功处理: {}条，错误: {}条",
                    batchId, processedCount, errorCount);
            Response.ok(true);

        } catch (Exception e) {
            log.error("UUID数据订正执行失败", e);
            Response.error(CodeEnum.DATA_ERROR.code(), "UUID数据订正执行失败: " + e.getMessage());
        }
    }

    /**
     * 处理权限规则的value字段，添加UUID信息
     */
    private String processRuleValue(String originalValue, Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap, Long datasetId) {
        try {
            if (null == datasetId) {
                throw new BiException(StandardErrorCode.SYSTEM_INTERNAL_ERROR, "数据集编码为空");
            }

            // 获取数据集字段配置
            List<DatasetColumnConfigDTO> datasetColumnConfig = getDatasetColumnConfig(columnConfigMap, datasetId);

            if (CollectionUtils.isEmpty(datasetColumnConfig)) {
                throw new BiException(StandardErrorCode.SYSTEM_INTERNAL_ERROR, "数据集字段配置为空");
            }

            boolean hasChanges = false;

            // 解析JSON数组
            JSONArray jsonArray = JSONUtil.parseArray(originalValue);
            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject) item;

                    // 处理conditionalObj中的enName字段（config和param表）
                    if (jsonObject.containsKey("conditionalObj")) {
                        JSONObject conditionalObj = jsonObject.getJSONObject("conditionalObj");
                        if (conditionalObj != null && conditionalObj.containsKey("enName")) {
                            String enName = conditionalObj.getStr("enName");
                            if (StringUtil.isNotEmpty(enName)) {
                                String uuid = matchDatasetUuid(enName, datasetColumnConfig);
                                conditionalObj.set("uuid", uuid);
                                hasChanges = true;
                            }
                        }
                        if (hasChanges) {
                            jsonObject.set("conditionalObj", conditionalObj);
                        }
                    }

                    // 处理datasetField字段（compute表）
                    if (jsonObject.containsKey("datasetField")) {
                        String datasetField = jsonObject.getStr("datasetField");
                        if (StringUtil.isNotEmpty(datasetField)) {
                            String uuid = matchDatasetUuid(datasetField, datasetColumnConfig);
                            jsonObject.set("uuid", uuid);
                            hasChanges = true;
                        }
                    }
                }
            }

            // 如果有变更，返回更新后的JSON字符串
            if (hasChanges) {
                return jsonArray.toString();
            } else {
                throw new BiException(StandardErrorCode.SYSTEM_INTERNAL_ERROR, "处理后的值与原始值相同");
            }

        } catch (Exception e) {
            log.error("处理权限规则value字段时发生错误: {}", originalValue, e);
            throw e;

        }
    }

    private String matchDatasetUuid(String enName, List<DatasetColumnConfigDTO> datasetColumnConfig) {
        for (DatasetColumnConfigDTO configDTO : datasetColumnConfig) {
            boolean hasEnName = StringUtils.isNotBlank(enName);

            if (hasEnName && enName.equals(configDTO.getEnName())) {
                return configDTO.getUuid();
            }
            if (hasEnName && enName.equals(configDTO.getOriginEnName())) {
                return configDTO.getUuid();
            }
            if (hasEnName && enName.contains("(" + configDTO.getOriginEnName())) {
                return configDTO.getUuid();
            }

            if (hasEnName && enName.contains("( " + configDTO.getOriginEnName())) {
                return configDTO.getUuid();
            }

            if (hasEnName && configDTO.getOriginEnName() != null && configDTO.getOriginEnName().contains("(" + enName)) {
                return configDTO.getUuid();
            }

        }

        throw new BusinessException("匹配失败, 原始信息 enName " + enName);
    }

    /**
     * 获取数据集字段配置
     */
    private List<DatasetColumnConfigDTO> getDatasetColumnConfig(Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                                                Long datasetId) {
        List<DatasetColumnConfigDTO> datasetColumnList = new ArrayList<>();
        if (columnConfigMap.containsKey(datasetId)) {
            datasetColumnList = columnConfigMap.get(datasetId);
        } else {
            DatasetColumnConfigRequest req = new DatasetColumnConfigRequest();
            req.setDatasetId(datasetId);
            Response<List<DatasetColumnConfigDTO>> response = datasetService.getColumnConfigList(req);
            if (response.isSuccess()) {
                datasetColumnList = response.getData();
                columnConfigMap.put(datasetId, datasetColumnList);
            }
        }
        return datasetColumnList;
    }

    /**
     * 带错误处理的权限规则value字段处理方法
     */
    private ProcessResult processRuleValueWithErrorHandling(String originalValue, Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                                            Long datasetId) {
        try {
            String processedValue = processRuleValue(originalValue, columnConfigMap, datasetId);
            return ProcessResult.success(processedValue);
        } catch (Exception e) {
            String errorType = determineErrorType(e);
            String stackTrace = getStackTrace(e);
            return ProcessResult.failure(errorType, e.getMessage(), stackTrace, originalValue);
        }
    }

    /**
     * 构建订正日志对象
     */
    private DatasetCorrectionLogDO buildCorrectionLog(String batchId,
                                                      DatasetRowAuthRuleDo rule,
                                                      ProcessResult result) {
        return DatasetCorrectionLogDO.builder()
                .batchId(batchId)
                .ruleId(rule.getId())
                .datasetId(rule.getDatasetId())
                .originalValue(rule.getValue())
                .processedValue(result.getProcessedValue())
                .status(result.isSuccess() ? DatasetCorrectionLogDO.Status.SUCCESS : DatasetCorrectionLogDO.Status.FAILURE)
                .errorType(result.getErrorType())
                .errorMessage(result.getErrorMessage())
                .stackTrace(result.getStackTrace())
                .createdAt(new Date())
                .createdBy("system")
                .build();
    }

    /**
     * 确定错误类型
     */
    private String determineErrorType(Exception e) {
        if (e.getMessage() != null) {
            if (e.getMessage().contains("JSON") || e.getMessage().contains("parse")) {
                return DatasetCorrectionLogDO.ErrorType.JSON_PARSE_ERROR;
            } else if (e.getMessage().contains("UUID") || e.getMessage().contains("mapping")) {
                return DatasetCorrectionLogDO.ErrorType.UUID_MAPPING_ERROR;
            } else if (e.getMessage().contains("data") || e.getMessage().contains("process")) {
                return DatasetCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR;
            }
        }
        return DatasetCorrectionLogDO.ErrorType.UNKNOWN_ERROR;
    }

    /**
     * 获取异常堆栈信息
     */
    private String getStackTrace(Exception e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString();
        } catch (Exception ex) {
            return "获取堆栈信息失败: " + ex.getMessage();
        }
    }
}
