package com.bestpay.bigdata.bi.report.beforeSQL.enums;

import com.bestpay.bigdata.bi.common.error.StandardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum PickerTypeEnum {

  /**
   * 选择
   */
  DATE_PICKER("datePicker","选择"),

  /**
   * 范围
   */
  DATE_RANGE("dateRange", "范围"),

  /**
   * 开始于
   */
  START_FORM("startFrom","开始于"),

  /**
   * 结束于
   */
  END_FORM("endFrom","结束于");

  @Getter
  private final String code;

  @Getter
  private final String msg;


  PickerTypeEnum(String code, String msg) {
    this.code = code;
    this.msg = msg;
  }

  public static PickerTypeEnum getEnum(String code) {
    PickerTypeEnum[] pickerTypeEnums = PickerTypeEnum.values();
    for (PickerTypeEnum pickerTypeEnum : pickerTypeEnums) {
      if (Objects.equals(pickerTypeEnum.getCode(), code)) {
        return pickerTypeEnum;
      }
    }

    throw new BiException(StandardErrorCode.UNSUPPORTED_PICKER_TYPE_ERROR, "不支持的筛选器类型, code=" + code);
  }
}
