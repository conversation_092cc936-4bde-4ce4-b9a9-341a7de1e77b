package com.bestpay.bigdata.bi.report.config;

import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.report.cache.CacheHandleFactory;
import com.bestpay.bigdata.bi.report.cache.UserCookieInfoCacheHandler;
import com.bestpay.bigdata.bi.report.cache.enums.SceneEnums;
import com.bestpay.bigdata.bi.report.service.embed.MobileEmailDecryptService;
import com.bestpay.bigdata.bi.report.service.embed.ReportAppEmbedService;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.bestpay.bigdata.bi.thirdparty.api.UserInfoService;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2021/3/18 9:28
 **/
@Configuration
public class WebConfig implements WebMvcConfigurer {
  @Resource
  private AiplusService aiplusService;
  @Resource
  private AiPlusUserService aiplusUserService;
  @Resource
  private ReportAppEmbedService reportAppEmbedService;
  @Resource
  private MobileEmailDecryptService mobileEmailDecryptService;
  @Resource
  private ApolloRefreshConfig apolloRefreshConfig;
  @Resource
  private UserInfoService userInfoService;
  @Resource
  private CacheHandleFactory cacheHandleFactory;

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(new CustomInterceptor(aiplusService,
            aiplusUserService,
            reportAppEmbedService,
            mobileEmailDecryptService,
            apolloRefreshConfig,
            userInfoService,
            (UserCookieInfoCacheHandler) cacheHandleFactory.getHandleStrategy(SceneEnums.USER_COOKIE)))
        .addPathPatterns("/**");
  }

  @Bean
  @Qualifier(DispatcherServletAutoConfiguration.DEFAULT_DISPATCHER_SERVLET_BEAN_NAME)
  public DispatcherServlet dispatcherServlet() {
    return new MyDispatcherServlet();
  }
}
