package com.bestpay.bigdata.bi.report.service.subscribe;

import com.bestpay.bigdata.bi.report.request.subscribe.AddSubScribeRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.UpdateSubScribeRequest;

public interface ReportSubService {
    /**
     * 新增报表订阅
     * @param request 请求参数
     * @return 报表订阅ID
     */
    Long saveReportSub(AddSubScribeRequest request);

    /**
     * 更新报表订阅
     * @param request 请求参数
     * @return 报表订阅ID
     */
    Long updateReportSub(UpdateSubScribeRequest request);

}
