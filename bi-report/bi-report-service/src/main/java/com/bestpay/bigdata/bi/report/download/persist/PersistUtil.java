package com.bestpay.bigdata.bi.report.download.persist;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.error.FileErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.util.PasswordUtil;
import com.bestpay.bigdata.bi.report.util.Zip4jUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * ClassName: PersistUtil
 * Package: com.bestpay.bigdata.bi.report.download.persist
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/12/13 15:50
 * @Version 1.0
 */
@Slf4j
public class PersistUtil
{
    private static final String FILE_ZIP = ".zip";

    public static CompressResult compressFilePath(String localFilePath,
                                                  Integer fileContainSensitiveInfo,
                                                  PasswordUtil passwordUtil) {

        CompressResult result = new CompressResult();

        int lastIndex = StrUtil.lastIndexOfIgnoreCase(localFilePath, StrUtil.DOT);
        String zipFilePath = StrUtil.subPre(localFilePath, lastIndex) + FILE_ZIP;
        result.setZipFilePath(zipFilePath);

        if (Objects.isNull(fileContainSensitiveInfo) || fileContainSensitiveInfo != 1) {
            try {
                Zip4jUtils.zipDir(zipFilePath, localFilePath,false);
            } catch (Exception e) {
                log.error("compressFile to zip error",e);
                throw new BiException(FileErrorCode.FILE_COMPRESS_FILE_ERROR,"compressFile to zip error:{}"+e.getMessage());
            }
            return result;
        }

        String password = passwordUtil.createPwdAndCheckSensitiveWord();
        try {
            Zip4jUtils.zipDir(zipFilePath, localFilePath, password,false);
        } catch (Exception e) {
            log.error("compressFile to zip error",e);
            throw new BiException(FileErrorCode.FILE_COMPRESS_FILE_ERROR,"compressFile to zip error:{}"+e.getMessage());
        }
        result.setPassword(Base64.encode(password));
        return result;
    }
}
