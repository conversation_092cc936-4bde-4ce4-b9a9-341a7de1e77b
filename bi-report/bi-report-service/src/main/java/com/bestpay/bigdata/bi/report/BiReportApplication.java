package com.bestpay.bigdata.bi.report;

import com.alibaba.dubbo.config.spring.context.annotation.EnableDubbo;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * bi-report 启动类
 * <AUTHOR>
 */
@EnableDubbo
@EnableApolloConfig
@SpringBootApplication(scanBasePackages = {
    "com.bestpay.bigdata.product.profile",
    "com.bestpay.bigdata.bi.database",
    "com.bestpay.bigdata.bi.thirdparty",
    "com.bestpay.bigdata.bi.common",
    "com.bestpay.bigdata.bi.report"
    })
@EnableAspectJAutoProxy
@EnableSwagger2
@EnableAsync
@EnableScheduling
public class BiReportApplication {

    public static void main(String[] args) {
        SpringApplication.run(BiReportApplication.class, args);
    }
}
