package com.bestpay.bigdata.bi.report.service.datascreen;

import com.bestpay.bigdata.bi.common.dto.datascreen.ComponentDTO;
import com.bestpay.bigdata.bi.report.request.new_datascreen.component.ComponentDetailQueryRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.component.ComponentQueryRequest;
import com.bestpay.bigdata.bi.report.response.datascreen.ComponentVO;
import java.util.List;

public interface ComponentService {

    List<ComponentVO> queryComponentList(ComponentQueryRequest request);

    List<ComponentDTO> queryComponentDetails(ComponentDetailQueryRequest request);
}
