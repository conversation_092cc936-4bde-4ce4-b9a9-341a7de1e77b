package com.bestpay.bigdata.bi.report.controller.dataset;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetRelationVO;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetRelationRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetRelationService;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023-07-05-17:31
 */
@RestController
@RequestMapping("/biReport/datasetRelation")
@Api(value = "数据集关系", tags = "数据集关系")
public class DatasetRelationController {

    @Resource
    private DatasetRelationService datasetRelationService;

    /**
     * 新增
     */
    @PostMapping("/add")
    @ApiOperation(httpMethod = "POST", value = "新增关系", notes = "新增关系")
    public Response<DatasetRelationVO> addDatasetRelation(@RequestBody DatasetRelationRequest datasetRelationRequest,
                                                         HttpServletRequest httpServletRequest) {

        return datasetRelationService.addDatasetRelation(datasetRelationRequest, httpServletRequest);
    }


    /**
     * 回显
     */
    @PostMapping("/show")
    @ApiOperation(httpMethod = "POST", value = "回显", notes = "回显")
    public Response<DatasetRelationVO> showDatasetRelation(@RequestBody DatasetRelationRequest datasetRelationRequest,
                                                          HttpServletRequest httpServletRequest) {
        return datasetRelationService.showDatasetRelation(datasetRelationRequest, httpServletRequest);
    }


}
