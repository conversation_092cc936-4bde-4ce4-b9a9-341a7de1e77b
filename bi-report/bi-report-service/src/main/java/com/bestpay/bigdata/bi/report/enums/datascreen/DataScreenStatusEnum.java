package com.bestpay.bigdata.bi.report.enums.datascreen;

import lombok.Getter;

/**
 * <AUTHOR>
 */

public enum DataScreenStatusEnum {

  PUBLISHED("published","已发布"),
  SAVED("saved","已保存"),
  OFFLINE("offline","待上线"),
  DELETE("delete","删除"),;

  @Getter
  private final String code;
  @Getter
  private final String msg;


  DataScreenStatusEnum(String code, String msg) {
    this.code = code;
    this.msg=msg;
  }


}
