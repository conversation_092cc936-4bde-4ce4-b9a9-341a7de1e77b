package com.bestpay.bigdata.bi.report.service.impl.dashboard;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.dto.dashboard.*;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewFilterCardDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewIndexCardDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewIndexCardDTO.IndexInfo;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewReportCardDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DataSet;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.report.BasicFormat;
import com.bestpay.bigdata.bi.common.dto.report.ColumnPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.IndexCardTypeEnum;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardIndexTextCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
import com.bestpay.bigdata.bi.report.bean.report.QueryReportConditionDTO;
import com.bestpay.bigdata.bi.report.cache.CacheHandleFactory;
import com.bestpay.bigdata.bi.report.cache.CommonCacheHandler;
import com.bestpay.bigdata.bi.report.cache.bean.ChartCacheBean;
import com.bestpay.bigdata.bi.report.cache.enums.SceneEnums;
import com.bestpay.bigdata.bi.report.enums.report.ReportTypeEnum;
import com.bestpay.bigdata.bi.report.request.report.QueryReportRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.service.common.EngineDataQueryService;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardCardDataQueryService;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardTableCardService;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.util.ReportUtil;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DashboardCardDataQueryServiceImpl
    implements DashboardCardDataQueryService {

  @Resource
  private DashboardIndexTextCardService indexTextCardService;
  @Resource
  private DatasetService datasetService;
  @Resource
  private DatePickerDAOService pickerDAOService;
  @Resource
  private DashboardTableCardService dashboardTableCardService;
  @Resource
  private EngineDataQueryService engineDataQueryService;

  @Resource
  private CacheHandleFactory cacheHandleFactory;

  @Resource
  private ApolloRefreshConfig apolloRefreshConfig;
  @Resource
  private DashboardReportCardService dashboardReportCardService;

  @Override
  public Object syncQueryData(TableCardDataRequest request) {

    log.info("request = {}", JSONUtil.toJsonStr(request));
    TableCardDataRequest requestForCache = JSONUtil.toBean(JSONUtil.toJsonStr(request), TableCardDataRequest.class);
    Long dashboardId = request.getDashboardId();
    NewCardTypeEnum cardTypeEnum = NewCardTypeEnum.getByCode(request.getCardType());
    DashBoardRelatedDTO dto  = DashBoardRelatedDTO.builder().type(AuthResourceTypeEnum.dashboard.name()).id(dashboardId).build();

    CommonCacheHandler handleStrategy = cacheHandleFactory.getHandleStrategy(SceneEnums.CHART);
    String redisKey = handleStrategy.generateKey(requestForCache);
    Boolean isRealtime = request.getIsRealtime();
    // 特殊仪表板缓存Id
    String northStarDashboardSpecialConfJson = apolloRefreshConfig.getNorthStarDashboardSpecialConf();
    NorthStarDashboardSpecialConf northStarDashboardSpecialConf = NorthStarDashboardSpecialConf.jsonToBean(northStarDashboardSpecialConfJson);
    List<Long> specialDashboardCacheIds = ReportUtil.strToLongList(northStarDashboardSpecialConf.getDashboardIdList());
    if (BooleanUtil.isFalse(isRealtime) && specialDashboardCacheIds.contains(dashboardId)) {
      ChartCacheBean chartCacheBean = ChartCacheBean.builder().redisKey(redisKey).request(requestForCache).build();
      log.info("进入查询缓存：{}",dashboardId);
      // 走缓存，
      Object cacheData = handleStrategy.getCacheData(chartCacheBean);
      if (Objects.nonNull(cacheData)) {
        log.info("缓存中取到了数据：{}",dashboardId);
        QueryIndexAndReportResponse bean = JSONUtil.toBean(JSONUtil.toJsonStr(cacheData), QueryIndexAndReportResponse.class);
        return Response.ok(bean);
      }
    }
    Response<QueryIndexAndReportResponse> response;
    switch (cardTypeEnum) {
      case REPORT:
        response =  handlerReportCardData(request,dto);
        break;
      case INDEX_TEXT:
        response = handlerIndexCardData(request,dto);
        break;
      case EMBED_TEXT_INDEX:
        response = handlerEmbedIndexCardData(request,dto);
        break;
      default:
        throw new BiException(DashboardErrorCode.DASHBOARD_NOT_SUPPORT_CARD_TYPE,"不支持的卡片类型, cardType" + request.getCardType() );
    }
    if (specialDashboardCacheIds.contains(dashboardId)){
      //  只有在Apollo配置了仪表板ID的才放入缓存
      QueryIndexAndReportResponse data = response.getData();
      handleStrategy.cacheData(ChartCacheBean.builder().redisKey(redisKey).request(requestForCache).value(data).build());
    }
    return response;
  }

  private Response<QueryIndexAndReportResponse> handlerIndexCardData(
      TableCardDataRequest request,DashBoardRelatedDTO dto) {

    log.info("handler index card data");

    // 指标信息
    TableCardInfoDTO info = dashboardTableCardService.getIndexTextCard(
        request.getDashboardId(), request.getCardCode(), null,request.getCardType(), IndexCardTypeEnum.EMBED_DASHBOARD.getCode());

    NewIndexCardDTO indexReport = (NewIndexCardDTO) info.getCardInfo();

    ReportRequest dataRequest = new ReportRequest();
    BeanUtils.copyProperties(request, dataRequest);
    // 过滤
    dataRequest.setFilterColumnList(getIndexFilter(indexReport));
    // 指标
    dataRequest.setIndexColumnList(indexReport.getDragResult());
    // 计算字段
    dataRequest.setComputeColumnList(indexReport.getCountFiledList());

    dataRequest.setReportType(ReportTypeEnum.AGG_TAB.getCode());
    dataRequest.setChartType(ChartTypeEnum.LIST_TABLE.getCode());

    // 数据集信息
    dataRequest.setDatasetInfoList(Lists.newArrayList(getDatasetInfo(indexReport)));
    // 筛选器参数
    dataRequest.setQueryConditions(request.getQueryConditions());
    // 参数筛选器
    dataRequest.setParamConditions(request.getParamConditions());
    dataRequest.setConditionList(Lists.newArrayList());
    dataRequest.setMaxRows("1");

    // SQL 执行
    return engineDataQueryService.getData(dataRequest,Boolean.TRUE,dto);
  }

  private Response<QueryIndexAndReportResponse> handlerEmbedIndexCardData(
      TableCardDataRequest request, DashBoardRelatedDTO dto) {
    // 查询引用指标文本卡片信息
    IndexCardQueryDTO find = new IndexCardQueryDTO();
    find.setIdList(Lists.newArrayList(Long.valueOf(request.getCardCode())));
    List<DashboardIndexTextCardDO> cardDOS = indexTextCardService.find(find);
    DashboardIndexTextCardDO embedIndex = cardDOS.get(0);

    ReportRequest dataRequest = new ReportRequest();
    BeanUtils.copyProperties(request, dataRequest);

    // 过滤
    dataRequest.setFilterColumnList(getEmbedIndexFilter(embedIndex));
    // 筛选器默认为空
    dataRequest.setConditionList(Lists.newArrayList());
    // 指标
    dataRequest.setIndexColumnList(
        JSONUtil.toList(embedIndex.getDragResult(), IndexComponentPropertyDTO.class));
    // 计算字段
    dataRequest.setComputeColumnList(JSONUtil
        .toList(embedIndex.getCountFiledList(), ComputeComponentPropertyDTO.class));
    // 报表类型
    dataRequest.setReportType(ReportTypeEnum.AGG_TAB.getCode());
    // 图标类型
    dataRequest.setChartType(ChartTypeEnum.LIST_TABLE.getCode());
    // 数据集信息
    dataRequest.setDatasetInfoList(Lists.newArrayList(getEmbedIndexDatasetInfo(embedIndex)));

    dataRequest.setQueryConditions(JSONUtil
        .toList(JSONUtil.toJsonStr(request.getQueryConditions()),QueryReportConditionInfo.class));
    dataRequest.setParamConditions(JSONUtil
        .toList(JSONUtil.toJsonStr(request.getParamConditions()),QueryReportConditionInfo.class));
    dataRequest.setMaxRows("1");

    // SQL 执行
    Response<QueryIndexAndReportResponse> response
        = engineDataQueryService.getData(dataRequest, Boolean.TRUE,dto);

    // 返回查询的指标文本卡片id
    if (response.getData() != null) {
      response.getData().setId(Long.valueOf(request.getCardCode()));
    }

    return response;
  }

  private Response<QueryIndexAndReportResponse> handlerReportCardData(
      TableCardDataRequest request,
      DashBoardRelatedDTO dto) {


    log.info("handler report card data");

    // 报表信息
    TableCardInfoDTO reportCardDTO = dashboardTableCardService.getReportCard(
        request.getDashboardId(), request.getCardCode());

    QueryReportRequest reportRequest = new QueryReportRequest();
    NewReportCardDTO cardDTO = (NewReportCardDTO) reportCardDTO.getCardInfo();

    // 字段折叠
    cardDTO.handlerFold();

    reportRequest.setReportId(cardDTO.getReportId());
    BeanUtils.copyProperties(request, reportRequest);

    // query data
    ReportRequest syncDateRequest
        = convertReportRequest((NewReportCardDTO) reportCardDTO.getCardInfo());

    BeanUtils.copyProperties(request,syncDateRequest);

    log.info("handlerReportCardData, queryConditions = {}",
        JSONUtil.toJsonStr(reportRequest.getQueryConditions()));

    log.info("handlerReportCardData, paramConditions = {}",
        JSONUtil.toJsonStr(reportRequest.getParamConditions()));

    // 查询条件
    syncDateRequest.setQueryConditions(getQueryConditions(request));

    // 参数条件
    syncDateRequest.setParamConditions(getParamConditions(request));

    // 报表默认排序 + 自定义排序
    if (CollUtil.isNotEmpty(request.getOrderColumns())) {
      syncDateRequest.setOrderColumnList(request.getOrderColumns()); // 自定义排序
    }
    // 查询报表数据
    Response<QueryIndexAndReportResponse> data = engineDataQueryService.getData(syncDateRequest, Boolean.TRUE, dto);
    // sprint 67 仪表板增加的分页功能，分页要显示总数，所以这里是加上了查询总条数
    TableConfiguration tableConfigurationObj = cardDTO.getTableConfigurationObj();
    if (Objects.nonNull(tableConfigurationObj) && syncDateRequest.getContrastColumnList().size() <= 1) {
      BasicFormat basicFormat = tableConfigurationObj.getBasicFormat();
      if (Objects.nonNull(basicFormat) && BooleanUtil.isTrue(basicFormat.getPageEnabled())){
        Long count = engineDataQueryService.getCount(syncDateRequest, UserContextUtil.getUserInfo());
        data.getData().setRowCount(count);
      }
    }
    return data;
  }

  private ReportRequest convertReportRequest(NewReportCardDTO reportCardDTO) {
    ReportRequest report
        = JSONUtil.toBean(JSONUtil.toJsonStr(reportCardDTO), ReportRequest.class);

    // 数据集信息
    List<DatasetInfo> datasets = reportCardDTO.getDatasetInfoList();

    List<DataSet> dataSets
        = datasetService.getDataSet(
        Lists.newArrayList(datasets.get(0).getDatasetId()));

    List<DatasetInfo> infos = dataSets.stream().map(p->{
      DatasetInfo info = new DatasetInfo();
      BeanUtils.copyProperties(p, info);
      return info;
    }).collect(Collectors.toList());

    report.setDatasetInfoList(infos);
    return report;
  }

  private static List<QueryReportConditionInfo> getParamConditions(
      TableCardDataRequest request) {
    List<QueryReportConditionInfo> paramconditionInfos = request.getParamConditions().stream().map(p->{
      QueryReportConditionInfo conditionInfo = new QueryReportConditionInfo();
      BeanUtils.copyProperties(p, conditionInfo);
      return conditionInfo;
    }).collect(Collectors.toList());
    return paramconditionInfos;
  }

  private List<QueryReportConditionInfo> getQueryConditions(
      TableCardDataRequest request) {
    List<QueryReportConditionInfo> conditionInfos = request.getQueryConditions().stream().map(p->{
      QueryReportConditionInfo conditionInfo = new QueryReportConditionInfo();


      TableCardInfoDTO cardInfoDTO
          = dashboardTableCardService.getFilterCard(request.getDashboardId(), p.getCardUuid());

      // 筛选器配置信息
      NewFilterCardDTO filterCardDTO = (NewFilterCardDTO) cardInfoDTO.getCardInfo();

      QueryReportConditionDTO conditionDTO
          = JSONUtil.toBean(JSONUtil.toJsonStr(filterCardDTO.getFields()), QueryReportConditionDTO.class);

      BeanUtils.copyProperties(conditionDTO, conditionInfo);
      BeanUtils.copyProperties(p, conditionInfo);

      return conditionInfo;
    }).collect(Collectors.toList());
    return conditionInfos;
  }

  @NotNull
  private DatasetInfo getEmbedIndexDatasetInfo(DashboardIndexTextCardDO embedIndex) {
    IndexInfo indexInfo = JSONUtil.toBean(embedIndex.getIndexInfo(), IndexInfo.class);
    List<DataSet> dbDataSets = datasetService.getDataSet(
        Lists.newArrayList(indexInfo.getDataSet()));
    DataSet dbDataSet = dbDataSets.get(0);

    DatasetInfo datasetInfo = new DatasetInfo();
    BeanUtils.copyProperties(dbDataSet, datasetInfo);
    return datasetInfo;
  }

  private List<FilterComponentPropertyDTO> getEmbedIndexFilter(
      DashboardIndexTextCardDO embedIndex) {
    List<ColumnPropertyDTO> filters = JSONUtil
        .toList(embedIndex.getFilterdragResult(), ColumnPropertyDTO.class);

    List<FilterComponentPropertyDTO> filterColumnList = Lists.newArrayList();
    for (ColumnPropertyDTO indexFilter : filters) {
      FilterComponentPropertyDTO conditionInfo = new FilterComponentPropertyDTO();
      BeanUtils.copyProperties(indexFilter, conditionInfo);

      // 日期类型过滤
      if(FieldType.DATETIME.name().equalsIgnoreCase(indexFilter.getShowTypeName())){
        DatePickerConfigDO pickerConfigDO = pickerDAOService.select(indexFilter.getDatePickerId());
        BeanUtils.copyProperties(pickerConfigDO, conditionInfo);
        conditionInfo.setDefaultValues(JSONUtil.toList(pickerConfigDO.getDefaultValue(), String.class));
        conditionInfo.setDateGroupType(null);
      }

      filterColumnList.add(conditionInfo);
    }
    return filterColumnList;
  }

  /**
   * 获取指标数据集信息
   * @param indexReport
   * @return
   */
  private DatasetInfo getDatasetInfo(NewIndexCardDTO indexReport) {
    List<DataSet> dbDataSets = datasetService
        .getDataSet(Lists.newArrayList(indexReport.getIndexInfo().getDataSet()));
    DataSet dbDataSet = dbDataSets.get(0);

    DatasetInfo datasetInfo = new DatasetInfo();
    BeanUtils.copyProperties(dbDataSet, datasetInfo);
    return datasetInfo;
  }

  /**
   * 获取指标文本：过滤项
   * @param indexReport
   * @return
   */
  private List<FilterComponentPropertyDTO> getIndexFilter(NewIndexCardDTO indexReport) {
    List<FilterComponentPropertyDTO> filterColumnList = Lists.newArrayList();
    for (FilterComponentPropertyDTO indexFilter : indexReport.getFilterdragResult()) {
      FilterComponentPropertyDTO conditionInfo = new FilterComponentPropertyDTO();
      BeanUtils.copyProperties(indexFilter, conditionInfo);

      // 日期类型过滤
      if(FieldType.DATETIME.name().equalsIgnoreCase(indexFilter.getShowTypeName())){
        conditionInfo.setDateGroupType(null);
      }

      filterColumnList.add(conditionInfo);
    }
    return filterColumnList;
  }

}
