package com.bestpay.bigdata.bi.report.cache.bean;

import com.bestpay.bigdata.bi.common.error.DatasetErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import lombok.Builder;
import lombok.Data;

import java.util.function.Function;

@Data
@Builder
public class DatasetCacheBean<K, V> {
    private V cacheValue;
    private K param;
    private Function<K, V> loader;
    private String loaderName;
    private String cacheKey;

    public V load() {
        if (loader == null || param == null) {
            throw new BiException(DatasetErrorCode.DATASET_CACHE_EXCEPTION_ERROR,"DatasetCacheBean: Loader or param cannot be null");
        }
        return loader.apply(param);
    }

}
