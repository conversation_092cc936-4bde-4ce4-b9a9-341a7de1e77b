package com.bestpay.bigdata.bi.report.service.impl.dashboard;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.common.TableFontStyleRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.GraphicDisplayDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewReportCardDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.report.ColumnPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.ReportScreeningConditionDTO;
import com.bestpay.bigdata.bi.common.dto.report.ReportSimpleColumn;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.report.TotalDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ConditionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ContrastComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.DimensionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.KeywordComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.OrderComponentDTO;
import com.bestpay.bigdata.bi.common.enums.DatePickerSourceEnum;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.util.ReportDTOInstantiateUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import com.bestpay.bigdata.bi.report.chart.handler.util.ReportOrderTypeEnum;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardReportCardProcessService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * ClassName: DashboardReportCardProcessServiceImpl
 * Package: com.bestpay.bigdata.bi.database.api.impl.dashboard
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/1/25 17:20
 * @Version 1.0
 */
@Slf4j
@Service
public class DashboardReportCardProcessServiceImpl implements DashboardReportCardProcessService
{
    @Resource
    private DatePickerDAOService pickerDAOService;

    /**
     * 新增报表模板
     *
     * @param reportCard
     * @return Response<Integer>
     */
    @Override
    public DashboardReportCardDO convertReportCardDo(NewReportCardDTO reportCard)
    {
        DashboardReportCardDO report = new DashboardReportCardDO();

        BeanUtils.copyProperties(reportCard, report);
        DatasetInfo datasetInfo = reportCard.getDatasetInfoList().get(0);
        report.setDatasetId(datasetInfo.getDatasetId());
        listToStr(reportCard, report);
        //坐标轴信息
        report.setCoordinateAxisConfig(JSONUtil.toJsonStr(reportCard.getCoordinateAxisConfigRequest()));

        // 卡片配置
        report.setCardStyleConfig(JSONUtil.toJsonStr(reportCard.getCardStyleConfig()));
        // 图形展示
        report.setGraphicDisplay(JSONUtil.toJsonStr(reportCard.getGraphicDisplayDTO()));
        // 如果没有orderType则默认给 常规排序
        if (Objects.isNull(reportCard.getOrderType())) {
            report.setOrderType(ReportOrderTypeEnum.DEFAULT.getType());
        }
        return report;
    }



    private void listToStr(NewReportCardDTO reportRequest, DashboardReportCardDO report) {
        // 对比字段
        report.setContrastColumn(JSONUtil
                .toJsonStr(reportRequest.getContrastColumnList()));

        // 维度
        report.setShowColumn(JSONUtil.toJsonStr(reportRequest.getShowColumnList()));

        // 指标
        report.setIndexColumn(JSONUtil.toJsonStr(reportRequest.getIndexColumnList()));

        // 筛选
        report.setCondition(JSONUtil.toJsonStr(handlerConditionColumnList(
                reportRequest.getConditionList())));

        // 关键字
        report.setKeyword(JSONUtil.toJsonStr(
                reportRequest.getKeywordList()));

        // 数据集
        report.setDatasetInfo(JSONUtil.toJsonStr(reportRequest.getDatasetInfoList()));

        // 过滤
        report.setFilterColumn(JSONUtil.toJsonStr(handlerFilterColumn(
                reportRequest.getFilterColumnList())));

        // 总计
        report.setShowIndexTotal(JSONUtil
                .toJsonStr(reportRequest.getShowIndexTotalObj()));

        // 报表结构
        report.setReportStructure(JSONUtil
                .toJsonStr(reportRequest.getReportStructureList()));

        // 报表样式
        report.setChartField(JSONUtil.toJsonStr(reportRequest.getChartFieldObj()));

        // 报表配置
        report.setTableConfiguration(JSONUtil
                .toJsonStr(reportRequest.getTableConfigurationObj()));

        // 排序
        report.setOrderColumn(JSONUtil.toJsonStr(reportRequest.getOrderColumnList()));

        // 计算字段
        report.setComputeColumn(JSONUtil.toJsonStr(reportRequest.getComputeColumnList()));

        // 表格字体格式
        report.setTableFontStyle(JSONUtil.toJsonStr(reportRequest.getTableFontStyle()));
    }

    private List<ColumnPropertyDTO> handlerConditionColumnList(List<ConditionComponentPropertyDTO> requests) {
        List<ColumnPropertyDTO> columnPropertyList = Lists.newArrayList();
        for (ConditionComponentPropertyDTO columnPropertyRequest : requests) {

            ColumnPropertyDTO columnProperty = new ColumnPropertyDTO();
            BeanUtils.copyProperties(columnPropertyRequest, columnProperty);

            ReportScreeningConditionDTO screeningCondition = new ReportScreeningConditionDTO();
            BeanUtils.copyProperties(columnPropertyRequest.getScreeningCondition(), screeningCondition);
            columnProperty.setScreeningCondition(screeningCondition);

            if(FieldType.DATETIME.name().equalsIgnoreCase(columnPropertyRequest.getShowTypeName())){

                DatePickerConfigDO pickerConfigDO = new DatePickerConfigDO();
                BeanUtils.copyProperties(columnPropertyRequest.getScreeningCondition(), pickerConfigDO);

                pickerConfigDO.setUpdatedAt(new Date());
                pickerConfigDO.setCreatedAt(new Date());
                pickerConfigDO.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
                pickerConfigDO.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
                pickerConfigDO.setSource(DatePickerSourceEnum.DASHBOARD_REPORT_CONDITION.getCode());
                pickerConfigDO.setDefaultValue(JSONUtil.toJsonStr(columnPropertyRequest
                        .getScreeningCondition().getDefaultValues()));

                Long datePickerId = pickerDAOService.insert(pickerConfigDO);
                screeningCondition.setDatePickerId(datePickerId);
            }

            columnPropertyList.add(columnProperty);
        }

        return columnPropertyList;
    }

    private List<ColumnPropertyDTO> handlerFilterColumn(List<FilterComponentPropertyDTO> requests) {
        List<ColumnPropertyDTO> columnPropertyList = Lists.newArrayList();
        for (FilterComponentPropertyDTO columnPropertyRequest : requests) {
            ColumnPropertyDTO columnProperty = new ColumnPropertyDTO();
            BeanUtils.copyProperties(columnPropertyRequest, columnProperty);

            if(FieldType.DATETIME.name().equalsIgnoreCase(columnPropertyRequest.getShowTypeName())){
                DatePickerConfigDO pickerConfigDO = new DatePickerConfigDO();
                BeanUtils.copyProperties(columnPropertyRequest, pickerConfigDO);

                pickerConfigDO.setUpdatedAt(new Date());
                pickerConfigDO.setCreatedAt(new Date());
                pickerConfigDO.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
                pickerConfigDO.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
                pickerConfigDO.setSource(DatePickerSourceEnum.DASHBOARD_REPORT_FILTER.getCode());
                pickerConfigDO.setDefaultValue(JSONUtil.toJsonStr(columnPropertyRequest.getDefaultValues()));

                Long datePickerId = pickerDAOService.insert(pickerConfigDO);
                columnProperty.setDatePickerId(datePickerId);
            }

            columnPropertyList.add(columnProperty);
        }

        return columnPropertyList;
    }

    @Override
    public NewReportCardDTO convertReportCardDTO(DashboardReportCardDO report) {

        NewReportCardDTO cardDTO = new NewReportCardDTO();
        BeanUtils.copyProperties(report, cardDTO);

        // 筛选
        cardDTO.setConditionList(getConditionColumnPropertyResponses(
                report.getCondition()));

        // 维度
        cardDTO.setShowColumnList(getDimensionColumnPropertyResponses(
                report.getShowColumn()));

        cardDTO.getShowColumnList().forEach(column -> column.setFieldType("DIMENSION"));

        // 指标
        cardDTO.setIndexColumnList(getIndexColumnPropertyResponses(
                report.getIndexColumn()));

        cardDTO.getIndexColumnList().forEach(column -> column.setFieldType("MEASURE"));

        // 关键字
        cardDTO.setKeywordList(getKeywordColumnPropertyResponses(
                report.getKeyword()));

        // 过滤
        cardDTO.setFilterColumnList(getFilterColumnPropertyResponses(
                report.getFilterColumn()));

        // 数据集信息
        cardDTO.setDatasetInfoList(getDatasetInfos(
                report));

        // 总计信息
        cardDTO.setShowIndexTotalObj(JSONUtil
                .toBean(report.getShowIndexTotal(), TotalDTO.class));

        // 报表数据集字段结构
        cardDTO.setReportStructureList(JSONUtil
                .toList(report.getReportStructure(), ReportSimpleColumn.class));

        // 报表样式
        cardDTO.setTableConfigurationObj(JSONUtil
                .toBean(report.getTableConfiguration(), TableConfiguration.class));

        // 对比字段
        cardDTO.setContrastColumnList(getContrastColumnPropertyResponses(
                report.getContrastColumn()));

        // 报表颜色组
        cardDTO.setChartFieldObj(JSONUtil.parseObj(report.getChartField()));

        // 排序
        cardDTO.setOrderColumnList(getOrderColumnPropertyResponses(
                report.getOrderColumn()));

        // 计算字段
        cardDTO.setComputeColumnList(getComputeColumnPropertyResponses(
                report.getComputeColumn()));

        /**
         * 图表样式相关配置
         */
        cardDTO.setTableFontStyle(JSONUtil.toBean(report.getTableFontStyle(), TableFontStyleRequest.class));
        if(StringUtils.isNotEmpty(report.getGraphicDisplay())) {
            cardDTO.setGraphicDisplayDTO(
                JSONUtil.toBean(report.getGraphicDisplay(), GraphicDisplayDTO.class));
        }

        String coordinateAxisConfig = report.getCoordinateAxisConfig();
        if (StrUtil.isNotBlank(coordinateAxisConfig)){
            cardDTO.setCoordinateAxisConfigRequest(ReportDTOInstantiateUtil.getInstantiate(cardDTO.getChartType(), coordinateAxisConfig));
        }

        return cardDTO;
    }

    private static List<DatasetInfo> getDatasetInfos(DashboardReportCardDO report) {
        List<DatasetInfo> datasetInfos = new ArrayList<>();
        try {
            datasetInfos = JSONUtil.toList(report.getDatasetInfo(), DatasetInfo.class);
        } catch (Exception e) {
            DatasetInfo datasetInfo = JSONUtil.toBean(report.getDatasetInfo(), DatasetInfo.class);
            datasetInfos.add(datasetInfo);
        }

        for (DatasetInfo datasetInfo : datasetInfos) {
            if (datasetInfo.getDataSourceType() == null) {
                log.info("dataSetId {}, dataSourceType {}", datasetInfo.getDatasetId(), 0);
                datasetInfo.setDataSourceTypeCode(0);
            }
        }
        return datasetInfos;
    }

    private List<ConditionComponentPropertyDTO> getConditionColumnPropertyResponses(
            String columnPropertyStr) {
        List<ColumnPropertyDTO> conditionLists
                = JSONUtil.toList(columnPropertyStr, ColumnPropertyDTO.class);

        List<ConditionComponentPropertyDTO> conditionResponseList = Lists.newArrayList();
        for (ColumnPropertyDTO columnProperty : conditionLists) {
            ConditionComponentPropertyDTO response = new ConditionComponentPropertyDTO();
            BeanUtils.copyProperties(columnProperty, response);

            if(columnProperty.getScreeningCondition()!=null) {
                ReportScreeningConditionDTO screeningCondition = new ReportScreeningConditionDTO();
                BeanUtils.copyProperties(columnProperty.getScreeningCondition(), screeningCondition);

                // 获取日期配置
                if (FieldType.DATETIME.name().equalsIgnoreCase(columnProperty.getShowTypeName())) {
                    if (columnProperty.getScreeningCondition() != null
                            && columnProperty.getScreeningCondition().getDatePickerId() != null) {

                        DatePickerConfigDO configDO
                                = pickerDAOService.select(columnProperty.getScreeningCondition().getDatePickerId());

                        BeanUtils.copyProperties(configDO, screeningCondition);
                        screeningCondition.setDefaultValues(
                                JSONUtil.toList(configDO.getDefaultValue(), String.class));
                    }
                }

                response.setScreeningCondition(screeningCondition);
            }

            conditionResponseList.add(response);
        }

        return conditionResponseList;
    }

    private List<FilterComponentPropertyDTO> getFilterColumnPropertyResponses(
            String columnPropertyStr) {
        List<ColumnPropertyDTO> conditionLists
                = JSONUtil.toList(columnPropertyStr, ColumnPropertyDTO.class);

        List<FilterComponentPropertyDTO> conditionResponseList = Lists.newArrayList();
        for (ColumnPropertyDTO columnProperty : conditionLists) {
            FilterComponentPropertyDTO response = new FilterComponentPropertyDTO();
            BeanUtils.copyProperties(columnProperty, response);

            // 获取日期配置
            if(FieldType.DATETIME.name().equalsIgnoreCase(columnProperty.getShowTypeName())){
                if(columnProperty.getDatePickerId()!=null){
                    DatePickerConfigDO configDO = pickerDAOService.select(columnProperty.getDatePickerId());
                    BeanUtils.copyProperties(configDO, response);
                    response.setDefaultValues(JSONUtil.toList(configDO.getDefaultValue(), String.class));
                }
            }

            conditionResponseList.add(response);
        }

        return conditionResponseList;
    }

    private List<OrderComponentDTO> getOrderColumnPropertyResponses(
            String columnPropertyStr) {
        List<OrderComponentDTO> conditionLists
                = JSONUtil.toList(columnPropertyStr, OrderComponentDTO.class);

        List<OrderComponentDTO> conditionResponseList = Lists.newArrayList();
        for (OrderComponentDTO columnProperty : conditionLists) {
            OrderComponentDTO response = new OrderComponentDTO();
            BeanUtils.copyProperties(columnProperty, response);
            conditionResponseList.add(response);
        }

        return conditionResponseList;
    }

    private List<KeywordComponentPropertyDTO> getKeywordColumnPropertyResponses(
        String columnPropertyStr) {
        List<ColumnPropertyDTO> conditionLists
            = JSONUtil.toList(columnPropertyStr, ColumnPropertyDTO.class);

        List<KeywordComponentPropertyDTO> conditionResponseList = Lists.newArrayList();
        for (ColumnPropertyDTO columnProperty : conditionLists) {
            KeywordComponentPropertyDTO response = new KeywordComponentPropertyDTO();
            BeanUtils.copyProperties(columnProperty, response);
            conditionResponseList.add(response);
        }

        return conditionResponseList;
    }

    private List<IndexComponentPropertyDTO> getIndexColumnPropertyResponses(
        String columnPropertyStr) {
        List<ColumnPropertyDTO> conditionLists
            = JSONUtil.toList(columnPropertyStr, ColumnPropertyDTO.class);

        List<IndexComponentPropertyDTO> conditionResponseList = Lists.newArrayList();
        for (ColumnPropertyDTO columnProperty : conditionLists) {
            IndexComponentPropertyDTO response = new IndexComponentPropertyDTO();
            BeanUtils.copyProperties(columnProperty, response);
            conditionResponseList.add(response);
        }

        return conditionResponseList;
    }

    private List<DimensionComponentPropertyDTO> getDimensionColumnPropertyResponses(
        String columnPropertyStr) {
        List<ColumnPropertyDTO> conditionLists
            = JSONUtil.toList(columnPropertyStr, ColumnPropertyDTO.class);

        List<DimensionComponentPropertyDTO> conditionResponseList = Lists.newArrayList();
        for (ColumnPropertyDTO columnProperty : conditionLists) {
            DimensionComponentPropertyDTO response = new DimensionComponentPropertyDTO();
            BeanUtils.copyProperties(columnProperty, response);
            conditionResponseList.add(response);
        }

        return conditionResponseList;
    }



    private List<ContrastComponentPropertyDTO> getContrastColumnPropertyResponses(
        String columnPropertyStr) {
        List<ColumnPropertyDTO> conditionLists
            = JSONUtil.toList(columnPropertyStr, ColumnPropertyDTO.class);

        List<ContrastComponentPropertyDTO> conditionResponseList = Lists.newArrayList();
        for (ColumnPropertyDTO columnProperty : conditionLists) {
            ContrastComponentPropertyDTO response = new ContrastComponentPropertyDTO();
            BeanUtils.copyProperties(columnProperty, response);
            conditionResponseList.add(response);
        }

        return conditionResponseList;
    }

    private List<ComputeComponentPropertyDTO> getComputeColumnPropertyResponses(
        String columnPropertyStr) {
        List<ColumnPropertyDTO> conditionLists
            = JSONUtil.toList(columnPropertyStr, ColumnPropertyDTO.class);

        List<ComputeComponentPropertyDTO> computeColumnPropertyResponseList = Lists.newArrayList();
        for (ColumnPropertyDTO columnProperty : conditionLists) {
            ComputeComponentPropertyDTO response = new ComputeComponentPropertyDTO();
            BeanUtils.copyProperties(columnProperty, response);
            computeColumnPropertyResponseList.add(response);
        }

        return computeColumnPropertyResponseList;
    }



}
