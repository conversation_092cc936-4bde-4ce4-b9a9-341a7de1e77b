package com.bestpay.bigdata.bi.report.schedule.subscribe.util;

import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.springframework.stereotype.Component;

/**
 * ClassName: ChromeDriverService
 * Package: com.bestpay.bigdata.bi.report.schedule
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/2/28 13:46
 * @Version 1.0
 */
@Slf4j
@Component
public class ChromeDriverUtil
{

    @Resource
    private ApolloRefreshConfig apolloRefreshConfig;


    public ChromeDriver initChromeDriver(String downloadFilepath) {
        System.setProperty("webdriver.chrome.driver", "/usr/bin/chromedriver");
        ChromeOptions options = new ChromeOptions();
        options.setHeadless(true);
        //ssl证书支持
        options.setCapability("acceptSslCerts", true);
        //截屏支持
        options.setCapability("takesScreenshot", true);
        //css搜索支持
        options.setCapability("cssSelectorsEnabled", true);

//    options.addArguments(CommonConfig.INSTANCE.getDriverArguments());
        options.setExperimentalOption("excludeSwitches", new String[]{"enable-automation"});
        options.addArguments("--window-size=1960,1080;");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-extensions");
        options.addArguments("--headless");
        options.addArguments("lang=zh_CN.UTF-8");
        // 设置上海时区
        options.addArguments("--timezone=Asia/Shanghai");
//        options.addArguments("--allow-http-screen-capture");

        HashMap<String, Object> prefs = new HashMap<String, Object>();
        prefs.put("download.default_directory", downloadFilepath);
        options.setExperimentalOption("prefs", prefs);

        ChromeDriver driver = new ChromeDriver(options);

        //设置超时，避免有些内容加载过慢导致截不到图
        driver.manage().timeouts().pageLoadTimeout(apolloRefreshConfig.getStatementQueryTimeoutSeconds(), TimeUnit.SECONDS);
        driver.manage().timeouts().implicitlyWait(apolloRefreshConfig.getStatementQueryTimeoutSeconds(), TimeUnit.SECONDS);
        driver.manage().timeouts().setScriptTimeout(apolloRefreshConfig.getStatementQueryTimeoutSeconds(), TimeUnit.SECONDS);

        return driver;
    }

}
