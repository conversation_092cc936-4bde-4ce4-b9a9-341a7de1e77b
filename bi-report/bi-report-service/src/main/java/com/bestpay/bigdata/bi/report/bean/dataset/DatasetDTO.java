package com.bestpay.bigdata.bi.report.bean.dataset;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @create 2023-07-05-13:32
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DatasetDTO implements Serializable {

    private static final long serialVersionUID = 7811683759852272343L;
    /**编码*/
    private String code;

    /**数据集名称*/
    private String name;

    /**数据集类型 明细表、结果表 @ReportTypeEnum*/
    private Integer type;

    /**应用场景 @QueryType */
    private Integer applicationScenario;

    /**归属组织*/
    private String orgCode;

    /**组织权限*/
    private String orgAuth;

    /**数据源类型*/
    private String datasourceType;

    /**数据源名称*/
    private String datasourceName;

    /**状态*/
    private Integer statusCode;

    /**责任人*/
    private String owner;
}
