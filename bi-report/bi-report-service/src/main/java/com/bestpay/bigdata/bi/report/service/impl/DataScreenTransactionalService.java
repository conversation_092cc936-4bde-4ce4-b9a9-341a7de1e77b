package com.bestpay.bigdata.bi.report.service.impl;

import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.DataScreenStatusEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.datascreen.*;
import com.bestpay.bigdata.bi.database.dao.datascreen.ComponentDO;
import com.bestpay.bigdata.bi.report.service.datascreen.DataScreenMainService;
import com.bestpay.bigdata.bi.report.service.impl.datascreen.ComponentFactory;
import com.bestpay.bigdata.bi.report.service.impl.datascreen.componentHandler.ComponentHandler;
import com.bestpay.bigdata.bi.report.service.impl.datascreen.componentHandler.ComponentHandlerFactory;
import com.bestpay.bigdata.bi.report.service.impl.report.component.ComponentHandlerRegister;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class DataScreenTransactionalService {

    @Resource
    private DataScreenDaoService dataScreenDaoService;

    @Resource
    private DataScreenMainService dataScreenMainService;

    @Resource
    private DataScreenConfDaoService dataScreenConfDaoService;

    @Resource
    private ComponentDaoService componentDaoService;

    @Resource
    private ComponentLayerDaoService componentLayerDaoService;

    @Resource
    private ComponentLayerEventDaoService componentLayerEventDaoService;

    @Resource
    private ComponentHandlerFactory componentHandlerFactory;

    @Transactional
    public void listPublish(Long dataScreenId, DataScreenStatusEnum dataScreenStatusEnum, Long toUpdateId ,Long toDeleteId ){
        dataScreenMainService.updateStatusByDataScreenId(dataScreenId,dataScreenStatusEnum.getCode());
        if (Objects.nonNull(toUpdateId)){
            dataScreenDaoService.updateById(toUpdateId,dataScreenStatusEnum);
        }
        if (Objects.nonNull(toDeleteId)){
            UserInfo userInfo = UserContextUtil.get();
            dataScreenDaoService.delete(toDeleteId,userInfo.getAccount());
        }
    }

    @Transactional
    public void updateStatus(List<String> dataScreenUuidList, Long dataScreenId, String status, UserInfo userInfo) {
        String account = userInfo.getAccount();
        dataScreenDaoService.updateByDataScreenUuidList(dataScreenUuidList,status);
        dataScreenMainService.updateStatusByDataScreenId(dataScreenId,status);

        if (StringUtils.isNoneBlank(status) && (DataScreenStatusEnum.DELETE.getCode()).equals(status)) {
            dataScreenConfDaoService.delete(dataScreenId, account);

            // 获取数据大屏下所有组件主表数据
            List<ComponentDO> componentDOList = componentDaoService.findByCode(dataScreenId, null);

            componentDOList.forEach(componentDO -> {
                // 删除组件主表数据
                componentDaoService.delete(dataScreenId, account);

                // 删除组件图层数据
                componentLayerDaoService.delete(componentDO.getLayerId(), account);

                // 删除组件图层事件数据
                componentLayerEventDaoService.delete(dataScreenId, componentDO.getCardUniqueKey(), account);

                // 删除对应组件详情数据
                deleteComponentDetails(componentDO, account);
            });
        }
    }

    private void deleteComponentDetails(ComponentDO componentDO, String account) {
        ComponentHandler componentStoreHandler = componentHandlerFactory.getComponentStoreHandler(componentDO.getCardType());
        if (componentStoreHandler != null) {
            componentStoreHandler.deleteComponent(componentDO, account);
        }
    }
}
