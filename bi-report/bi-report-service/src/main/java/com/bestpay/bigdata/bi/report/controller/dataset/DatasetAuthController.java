package com.bestpay.bigdata.bi.report.controller.dataset;

import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetAuthUserListVO;
import com.bestpay.bigdata.bi.report.request.dataset.*;
import com.bestpay.bigdata.bi.report.response.dataset.DatasetAuthListVO;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetAuthService;
import java.util.List;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/biReport/dataset/auth")
@Api(value = "数据集权限", tags = "数据集权限")
public class DatasetAuthController {

    @Resource
    private DatasetAuthService datasetAuthService;


    /**
     * 获取用户|用户组列表
     */
    @PostMapping("/getAllUserAndUserGroup")
    @ApiOperation(httpMethod = "POST", value = "获取用户|用户组列表", notes = "获取用户|用户组列表")
    public Response<List<DatasetAuthUserListVO>> getAllUserAndUserGroup() {
        List<DatasetAuthUserListVO> data = datasetAuthService.getAllUserAndUserGroup();
        return Response.ok(data);
    }

    /**
     * 获取用户列表（分页+关键字）
     */
    @PostMapping("/getUsers")
    @ApiOperation(httpMethod = "POST", value = "获取用户列表（分页+关键字）", notes = "获取用户列表（分页+关键字）")
    public Response<PageQueryVO<DatasetAuthUserListVO>> getUsers(@RequestBody UserAndGroupRequest request) {
        PageQueryVO<DatasetAuthUserListVO> data = datasetAuthService.getUserList(request);
        return Response.ok(data);
    }

    /**
     * 获取用户组列表（关键字）
     */
    @PostMapping("/getUserGroups")
    @ApiOperation(httpMethod = "POST", value = "获取用户组列表（关键字）", notes = "获取用户组列表（关键字）")
    public Response<List<DatasetAuthUserListVO>> getUserGroups(@RequestBody UserAndGroupRequest request) {
        List<DatasetAuthUserListVO> data = datasetAuthService.getUserGroupList(request);
        return Response.ok(data);
    }



    /**
     * 添加白名单
     */
    @PostMapping("/addWhiteUser")
    @ApiOperation(httpMethod = "POST", value = "添加白名单", notes = "添加白名单")
    public Response<List<DatasetAuthUserListVO>> addWhiteUser(@RequestBody DatesetAuthWhiteUserRequest request) {
        datasetAuthService.addWhiteUser(request);
        return Response.ok();
    }


    /**
     * 添加规则
     */
    @PostMapping("/addRule")
    @ApiOperation(httpMethod = "POST", value = "添加规则", notes = "添加规则")
    public Response addRule(@RequestBody @Validated DatesetAuthAddRequest request) {
        datasetAuthService.addRule(request);
        return Response.ok();
    }

    /**
     * 添加规则
     */
    @PostMapping("/editRule")
    @ApiOperation(httpMethod = "POST", value = "添加规则", notes = "添加规则")
    public Response editRule(@RequestBody @Validated DatesetAuthAddRequest request) {
        datasetAuthService.editRule(request);
        return Response.ok();
    }


    /**
     * 规则列表
     */
    @PostMapping("/ruleList")
    @ApiOperation(httpMethod = "POST", value = "规则列表", notes = "规则列表")
    public Response<DatasetAuthListVO> ruleList(@RequestBody DatesetAuthListRequest request) {
        return datasetAuthService.ruleList(request);
    }


    /**
     * 规则状态
     */
    @PostMapping("/updateRuleStatus")
    @ApiOperation(httpMethod = "POST", value = "规则状态", notes = "规则状态")
    public Response ruleList(@RequestBody DatesetAuthStatusUpdateRequest request) {
        return datasetAuthService.updateRuleStatus(request);
    }
}
