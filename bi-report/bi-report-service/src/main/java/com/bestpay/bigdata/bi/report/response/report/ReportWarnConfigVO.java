package com.bestpay.bigdata.bi.report.response.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("报表告警配置")
public class ReportWarnConfigVO {
    @ApiModelProperty(value = "唯一标识UUID")
    private String uuid;
    @ApiModelProperty(value = "报表配置的唯一标识")
    protected String configUuid;
    @ApiModelProperty(value = "报表字段")
    private String reportField;
    @ApiModelProperty(value = "字段显示类型")
    private String showTypeName;
    @ApiModelProperty(value = "告警名称")
    private String name;
    @ApiModelProperty(value = "日期组类类型")
    private Integer dateGroupType;
    @ApiModelProperty(value = "聚合方式")
    private String polymerization;
}
