package com.bestpay.bigdata.bi.report.chart.handler;

import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.report.chart.bean.ChartData;
import com.bestpay.bigdata.bi.report.chart.bean.ChartHeader;
import com.bestpay.bigdata.bi.report.chart.bean.ChartResult;
import java.util.List;

/**
 * ClassName: Chart
 * Package: com.bestpay.bigdata.bi.report.chart
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/3/13 9:55
 * @Version 1.0
 */
public interface Chart
{
    ChartHeader getChartHeader();

    ChartData getChartData(ChartHeader header, List<List<String>> baseData);

    ChartTypeEnum getChartType();

    ChartResult processHideFormat(ChartHeader header, ChartData data);

    Long getChartRowCount();
}
