package com.bestpay.bigdata.bi.report.service.handler;

import com.bestpay.bigdata.bi.common.dto.dashboard.TableCardInfoDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewFilterCardDTO;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardFilterCardService;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardTableCardService;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @author:gaodingsong
 * @description:
 * @createTime:2024/4/17 15:08
 * @version:1.0
 */
@Service("filter")
public class FilterDashboardHandler implements DashboardHandler {

  @Resource
  private DashboardFilterCardService filterCardService;

  @Resource
  private DashboardTableCardService dashboardTableCardService;

  @Override
  public Long copyAndInsert(Long oldDashboard,
      String oldCode,
      Long cardId,
      Long newDashboardId,
      Map<String, String> cardCodeMap,
      Map<Long, Long> embedTextIdMap,
      String cardName,
      String indexType) {

    TableCardInfoDTO cardInfoDTO = dashboardTableCardService.getFilterCard(oldDashboard, oldCode);
    NewFilterCardDTO filterCard = (NewFilterCardDTO) cardInfoDTO.getCardInfo();

    // 关联卡片id重新生成了，需要进行替换
    for (NewFilterCardDTO.RelateCardInfo relateCardInfo : filterCard.getRelateCardsInfo()) {
      relateCardInfo.setCardId(cardCodeMap.get(relateCardInfo.getCardId()));
      relateCardInfo.setValue(cardCodeMap.get(relateCardInfo.getValue()));
    }

    // 重新生成新的configUuid
    NewFilterCardDTO.Fields fields = filterCard.getFields();
    if (fields != null) {
      fields.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());

    }

    DashboardFilterCardDO filterCardDO = DashboardFilterCardDO.toDTO(filterCard,
        UserContextUtil.get(), newDashboardId);

    return filterCardService.insert(filterCardDO);
  }
}
