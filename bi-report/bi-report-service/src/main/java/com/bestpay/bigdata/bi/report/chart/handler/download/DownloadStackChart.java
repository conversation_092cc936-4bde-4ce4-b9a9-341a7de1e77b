package com.bestpay.bigdata.bi.report.chart.handler.download;

import com.bestpay.bigdata.bi.backend.api.QueryService;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.report.chart.bean.ChartContext;
import com.bestpay.bigdata.bi.report.chart.bean.ChartData;
import com.bestpay.bigdata.bi.report.chart.bean.ChartHeader;
import com.bestpay.bigdata.bi.report.chart.bean.ReportBaseModel;
import com.bestpay.bigdata.bi.report.chart.handler.AbstractChart;
import com.bestpay.bigdata.bi.report.service.common.EngineDataQueryService;
import com.bestpay.bigdata.bi.report.sql.SemanticManager;
import java.util.List;

/**
 * 下载堆积图数据获取
 *
 * <AUTHOR>
 */
public class DownloadStackChart extends AbstractChart {

  SemanticManager semanticManager;

  public DownloadStackChart(ChartContext context,
      QueryService queryService,
      SemanticManager semanticManager,
      EngineDataQueryService dataQueryService) {
    super(context, queryService, dataQueryService);
    this.semanticManager = semanticManager;
  }

  @Override
  protected ChartData makeChartData(ChartHeader header, List<List<String>> baseData) {
    ReportBaseModel reportBaseModel = context.getReportBaseModel();
    //和ListTableChart一致
    return reportBaseModel.getContrastColumnList().size() > 1 ?
        new DownloadContrastTableChart(context, queryService,semanticManager , dataQueryService).getChartData(header, baseData) :
        new DownloadListTableChart(context, queryService, dataQueryService).getChartData(header, baseData);
  }

  @Override
  public ChartTypeEnum getChartType() {
    return ChartTypeEnum.STACK_CHART;
  }

  @Override
  public ChartHeader getChartHeader() {
    ReportBaseModel reportBaseModel = context.getReportBaseModel();
    //和直接转换成ListTableChart一致
    return reportBaseModel.getContrastColumnList().size() > 1 ?
        new DownloadContrastTableChart(context, queryService, semanticManager, dataQueryService).getChartHeader() :
        new DownloadListTableChart(context, queryService, dataQueryService).getChartHeader();
  }
}
