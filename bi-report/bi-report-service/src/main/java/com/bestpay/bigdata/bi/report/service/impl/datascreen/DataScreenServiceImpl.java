package com.bestpay.bigdata.bi.report.service.impl.datascreen;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.datascreen.ComponentDTO;
import com.bestpay.bigdata.bi.common.dto.datascreen.ComponentInfo;
import com.bestpay.bigdata.bi.common.dto.datascreen.DataScreenInfo;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.ComponentDataQueryType;
import com.bestpay.bigdata.bi.common.enums.ComponentTypeEnum;
import com.bestpay.bigdata.bi.common.enums.DataScreenStatusEnum;
import com.bestpay.bigdata.bi.common.enums.DataScreenVersionTypeEnum;
import com.bestpay.bigdata.bi.common.enums.PlatformEnum;
import com.bestpay.bigdata.bi.common.enums.ReportResourceTypeEnum;
import com.bestpay.bigdata.bi.common.error.DataScreenErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.LogUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.datascreen.*;
import com.bestpay.bigdata.bi.database.dao.datascreen.ComponentDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.ComponentLayerDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.DataScreenConfDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.DataScreenDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.DataScreenMainDO;
import com.bestpay.bigdata.bi.report.enums.embed.AppEmbedTypeEnum;
import com.bestpay.bigdata.bi.report.enums.embed.NetworkTypeEnum;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenPartakeRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenQueryRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenSaveRequest;
import com.bestpay.bigdata.bi.report.response.datascreen.DataScreenSaveResult;
import com.bestpay.bigdata.bi.report.response.datascreen.DataScreenVO;
import com.bestpay.bigdata.bi.report.service.datascreen.DataScreenService;
import com.bestpay.bigdata.bi.report.service.embed.DataScreenAppEmbedService;
import com.bestpay.bigdata.bi.report.service.impl.datascreen.componentHandler.ComponentHandler;
import com.bestpay.bigdata.bi.report.service.impl.datascreen.componentHandler.ComponentHandlerFactory;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-08-27
 */
@Slf4j
@Service
public class DataScreenServiceImpl implements DataScreenService {


    @Resource
    private DataScreenDaoService dataScreenDaoService;

    @Resource
    private DataScreenConfDaoService dataScreenConfDaoService;

    @Resource
    private ComponentDaoService componentDaoService;

    @Resource
    private ComponentLayerDaoService componentLayerDaoService;

    @Resource
    private ComponentLayerEventDaoService componentLayerEventDaoService;

    @Resource
    private ComponentFactory componentFactory;

    @Resource
    private ComponentHandlerFactory componentHandlerFactory;

    @Resource
    private DataScreenMainDaoService dataScreenMainDaoService;

    @Resource
    private DataScreenAppEmbedService dataScreenAppEmbedService;

    private static final String DRAFT_VERSION_TYPE = DataScreenVersionTypeEnum.DRAFT.getCode();

    private static final String PUBLISHED_VERSION_TYPE = DataScreenVersionTypeEnum.PUBLISHED.getCode();

    private static final String DATASCREEN_RESOURCE_TYPE = ReportResourceTypeEnum.DATASCREEN.getCode();

    private static final String SAVED_STATUS = DataScreenStatusEnum.SAVED.getCode();

    private static final String PUBLISHED_STATUS = DataScreenStatusEnum.PUBLISHED.getCode();

    private static final ComponentDataQueryType REDIS_THEN_MYSQL = ComponentDataQueryType.REDIS_THEN_MYSQL;

    @Override
    @Transactional(rollbackFor = {Exception.class, BusinessException.class})
    public void save(DataScreenSaveRequest request) {
        UserInfo user = UserContextUtil.getUserInfo();

        //获取组件信息-redis-db
        Map<String, String> map = new HashMap<>();
        log.info("save data for dataScreenUUID: {}", request.getDataScreenUUID());

        //判断是否已经存在草稿版本,如存在，优先获取草稿版本的组件信息
        List<DataScreenDO> dataScreenList = dataScreenDaoService.findDataScreen(request.getDataScreenUUID(), DRAFT_VERSION_TYPE);
        List<DataScreenDO> offlineDataScreenList = dataScreenDaoService.findDataScreen(request.getDataScreenUUID(), DataScreenVersionTypeEnum.OFFLINE.getCode());


        if (dataScreenList.size() > 0) {
            log.debug("Draft version found, fetching components");
            getComponent(request.getDataScreenUUID(), request.getData(), map, REDIS_THEN_MYSQL, DRAFT_VERSION_TYPE);
        } else if (offlineDataScreenList.size() > 0){
            log.debug("No draft version found, fetching offline components");
            getComponent(request.getDataScreenUUID(), request.getData(), map, REDIS_THEN_MYSQL, DataScreenVersionTypeEnum.OFFLINE.getCode());
        }else {
            log.debug("No draft version found, fetching published components");
            getComponent(request.getDataScreenUUID(), request.getData(), map, REDIS_THEN_MYSQL, PUBLISHED_VERSION_TYPE);
        }

        DataScreenDO dataScreen = DataScreenDO.builder()
                .dataScreenUuid(request.getDataScreenUUID()).build();
        List<DataScreenDO> dataScreenDOList = dataScreenDaoService.findByDataScreenInfo(dataScreen);

        //删除原有数据大屏信息和组件信息
        delete(dataScreenDOList, DRAFT_VERSION_TYPE, user);

        String ownerEmail = CollUtil.isNotEmpty(dataScreenDOList) ? dataScreenDOList.get(0).getOwnerEmail() : user.getEmail();


        DataScreenMainDO dataScreenMainDO = dataScreenMainDaoService.findByDataScreenUUID(request.getDataScreenUUID());



        String status;
        if (null != dataScreenMainDO && DataScreenStatusEnum.OFFLINE == DataScreenStatusEnum.getByCode(dataScreenMainDO.getStatus())) {
            status = DataScreenStatusEnum.OFFLINE.getCode();
        } else {
            status = SAVED_STATUS;
        }


        //保存数据大屏主表信息好配置信息
        DataScreenSaveResult saveResult = storeDataScreen(request, DRAFT_VERSION_TYPE, status, user, ownerEmail);

        // 保存数据大屏信息和组件信息
        storeComponentList(saveResult.getDataScreenId(), request.getData(), map, DRAFT_VERSION_TYPE, user);

        log.info("Data screen saved successfully");

        //清理缓存
        componentFactory.getComponent().clearDataScreenCache(request.getDataScreenUUID());
    }

    public void clearDataScreenCache(String dataScreenUUID) {
        //清理缓存
        componentFactory.getComponent().clearDataScreenCache(dataScreenUUID);
    }


    @Override
    public DataScreenVO queryDataScreenInfo(DataScreenQueryRequest dataScreenQueryRequest) {
        clearDataScreenCache(dataScreenQueryRequest.getDataScreenUUID());


        List<DataScreenDO> dataScreen = dataScreenDaoService.findDataScreen(dataScreenQueryRequest.getDataScreenUUID(), dataScreenQueryRequest.getVersionType());
        DataScreenDO dataScreenDO = dataScreen.get(0);
        DataScreenConfDO dataScreenConf = dataScreenConfDaoService.findByResourceId(dataScreenDO.getId());

        DataScreenMainDO dataScreenStatus = dataScreenMainDaoService.findByDataScreenUUID(dataScreenQueryRequest.getDataScreenUUID());
        DataScreenVO dataScreenVO = new DataScreenVO();
        dataScreenVO.setOrgCode(dataScreenDO.getOrgCode());
        dataScreenVO.setName(dataScreenDO.getName());
        dataScreenVO.setOwnerEmail(dataScreenDO.getOwnerEmail());
        dataScreenVO.setTheme(dataScreenConf.getTheme());
        dataScreenVO.setFont(dataScreenConf.getFont());
        dataScreenVO.setHeight(dataScreenConf.getHeight());
        dataScreenVO.setWidth(dataScreenConf.getWidth());
        dataScreenVO.setBgColor(dataScreenConf.getBgColor());
        dataScreenVO.setBgImageUrl(dataScreenConf.getBgImageUrl());
        dataScreenVO.setScaleType(dataScreenConf.getScaleType());
        dataScreenVO.setStatus(dataScreenStatus.getStatus());
        dataScreenVO.setId(dataScreenDO.getId());
        dataScreenVO.setSize(dataScreenConf.getSize());
        dataScreenVO.setRefreshTime(dataScreenConf.getRefreshTime());
        dataScreenVO.setLastUpdatedAt(dataScreenStatus.getUpdatedAt());
        dataScreenVO.setVersionType(dataScreenQueryRequest.getVersionType());
        return dataScreenVO;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, BusinessException.class})
    public void publish(DataScreenSaveRequest request) {
        publishDataScreen(request);
    }

    private Long publishDataScreen(DataScreenSaveRequest request) {
        UserInfo user = UserContextUtil.getUserInfo();

        // 获取组件信息-redis-db
        Map<String, String> map = new HashMap<>();
        log.info("publish data screen for dataScreenUUID: {}", request.getDataScreenUUID());

        // 判断是否已经存在发布版本
        List<DataScreenDO> dataScreenList = dataScreenDaoService.findDataScreen(request.getDataScreenUUID(), DRAFT_VERSION_TYPE);
        if (dataScreenList.size() > 0) {
            log.debug("Draft version found, fetching components");
            getComponent(request.getDataScreenUUID(), request.getData(), map, REDIS_THEN_MYSQL, DRAFT_VERSION_TYPE);
        } else {
            log.debug("No draft version found, fetching published components");
            getComponent(request.getDataScreenUUID(), request.getData(), map, REDIS_THEN_MYSQL, PUBLISHED_VERSION_TYPE);
        }

        DataScreenDO dataScreen = DataScreenDO.builder()
                .dataScreenUuid(request.getDataScreenUUID()).build();
        List<DataScreenDO> dataScreenDOList = dataScreenDaoService.findByDataScreenInfo(dataScreen);
        // 删除原有数据大屏信息和组件信息
        delete(dataScreenDOList, PUBLISHED_STATUS, user);
        String ownerEmail = CollUtil.isNotEmpty(dataScreenDOList) ? dataScreenDOList.get(0).getOwnerEmail() : user.getEmail();
        // 保存数据大屏主表信息和配置信息
        DataScreenSaveResult saveResult = storeDataScreen(request, PUBLISHED_STATUS, PUBLISHED_STATUS, user, ownerEmail);

        // 保存数据大屏信息和组件信息
        storeComponentList(saveResult.getDataScreenId(), request.getData(), map, PUBLISHED_STATUS, user);

        log.info("Data screen published successfully");
        //清理缓存
        componentFactory.getComponent().clearDataScreenCache(request.getDataScreenUUID());
        return saveResult.getDataScreenMainsId();
    }


    @Override
    @Transactional(rollbackFor = {Exception.class, BusinessException.class})
    public String shareDataScreen(DataScreenPartakeRequest request) {
        UserInfo user = UserContextUtil.getUserInfo();


        Long dataScreenMainsId;
        if (request.getIsPublish()) { //需要发布
            DataScreenSaveRequest saveRequest = new DataScreenSaveRequest();
            BeanUtils.copyProperties(request, saveRequest);
            //发布数据大屏
            dataScreenMainsId = publishDataScreen(saveRequest);
            log.info("Data screen published with ID: {}", dataScreenMainsId);
        } else {
            DataScreenMainDO dataScreenMainDO = dataScreenMainDaoService.findByDataScreenUUID(request.getDataScreenUUID());

            Preconditions.checkArgument(DataScreenStatusEnum.PUBLISHED.getCode().equals(dataScreenMainDO.getStatus()), "数据大屏不是已发布状态，不能分享");
            log.info("Data screen is in published status");

            dataScreenMainsId = dataScreenMainDO.getId();
            log.info("dataScreenMainsId: {}", dataScreenMainsId);
        }

        Response result = saveAppEmbed(dataScreenMainsId, request, user);
        log.info("App embed saved successfully");

        return result.getData().toString();
    }


    private Response<String> saveAppEmbed(Long dataScreenMainsId, DataScreenPartakeRequest request, UserInfo user) {
        AppEmbedRequest appEmbedRequest = AppEmbedRequest.builder()
                .embedObjectId(dataScreenMainsId)
                .embedObjectName(request.getDataScreenInfo().getName())
                .ownerName(user.getName())
                .email(user.getEmail())
                .orgCode(user.getOrg().getCode())
                .embedType(AppEmbedTypeEnum.NEW_DATASCREEN.getCode())
                .isPublishMobile(0)
                .networkType(NetworkTypeEnum.INSIDE.getCode())
                .platform(PlatformEnum.BI.getCode()).versionType(PUBLISHED_VERSION_TYPE)
                .taskName(request.getDataScreenInfo().getName())  //todo 拼接任务名称
                .build();
        return dataScreenAppEmbedService.completeAppEmbed(appEmbedRequest);
    }


    private DataScreenSaveResult storeDataScreen(DataScreenSaveRequest request, String versionType, String stauts, UserInfo user, String ownerEmail) {
        DataScreenInfo dataScreenInfo = request.getDataScreenInfo();

        Long dataScreenId = dataScreenDaoService.insert(DataScreenDO.builder()
                .name(dataScreenInfo.getName())
                .orgCode(user.getOrg().getCode())
                .ownerEmail(ownerEmail)
                .versionType(versionType)
                .dataScreenUuid(request.getDataScreenUUID())
                .previewUrl(request.getPreviewUrl())
                .createdBy(user.getAccount())
                .updatedBy(user.getAccount())
                .build());

        dataScreenConfDaoService.insert(DataScreenConfDO.builder()
                .resourceId(dataScreenId)
                .theme(dataScreenInfo.getTheme())
                .font(dataScreenInfo.getFont())
                .height(dataScreenInfo.getHeight())
                .width(dataScreenInfo.getWidth())
                .bgColor(dataScreenInfo.getBgColor())
                .bgImageUrl(dataScreenInfo.getBgImageUrl())
                .scaleType(dataScreenInfo.getScaleType())
                .createdBy(user.getAccount())
                .updatedBy(user.getAccount())
                .size(dataScreenInfo.getSize())
                .refreshTime(dataScreenInfo.getRefreshTime())
                .build());

        Long getDataScreenMainsId = dataScreenMainDaoService.insertOrUpdate(DataScreenMainDO.builder()
                .dataScreenUuid(request.getDataScreenUUID())
                .createdBy(user.getAccount())
                .updatedBy(user.getAccount())
                .status(stauts)
                .build());

        return new DataScreenSaveResult(dataScreenId, getDataScreenMainsId);
    }

    private void getComponent(String dataScreenUUID, List<ComponentInfo> componentInfoList,
                              Map<String, String> cardInfoMap, ComponentDataQueryType componentDataQueryType, String versionType) {
        for (ComponentInfo componentInfo : componentInfoList) {
            if (!componentInfo.getComponentType().equals(ComponentTypeEnum.TAB.getCode())) {
                AbstractComponent abstractComponent = componentFactory.getComponent(componentInfo.getComponentType());
                if (abstractComponent != null) {
                    ComponentDTO cardInfo = abstractComponent.getComponentInfo(dataScreenUUID, componentInfo.getComponentCode(),
                            versionType, componentDataQueryType);
                    if (cardInfo != null) {
                        cardInfoMap.put(componentInfo.getComponentCode(), JSONUtil.toJsonStr(cardInfo.getComponentInfo()));
                    }
                }
            }
            if (!CollUtil.isEmpty(componentInfo.getComponentInfoList())) {
                getComponent(dataScreenUUID, componentInfo.getComponentInfoList(), cardInfoMap, componentDataQueryType, versionType);
            }
        }
    }

    private void storeComponentList(Long dataScreenId,
                                    List<ComponentInfo> componentInfoList,
                                    Map<String, String> componentInfoMap,
                                    String versionType,
                                    UserInfo user) {

        for (ComponentInfo component : componentInfoList) {
            try {
                ComponentHandler componentStoreHandler = componentHandlerFactory.getComponentStoreHandler(component.getComponentType());
                if (componentStoreHandler != null) {
                    //保存组件详情信息
                    Long cardId = componentStoreHandler.storeComponent(componentInfoMap, dataScreenId, component, user);
                    //保存组件图层事件信息
                    componentStoreHandler.storeComponentLayerEvent(componentInfoMap, dataScreenId, component, user);
                    //保存组件主、组件图层表、组件状态表
                    insertMainTable(dataScreenId, component, component.getComponentType(), cardId, versionType, user);
                } else {
                    //保存时间组件、素材组件、tab组件
                    insertMainTable(dataScreenId, component, component.getComponentType(), null, versionType, user);
                }

            } catch (Exception e) {
                log.error("storeComponentList error, dataScreenId={}, componentCode={},exception={}", dataScreenId,
                        component.getComponentCode(), LogUtil.getStackTrace(e));
                throw new BiException(DataScreenErrorCode.DATA_SCREEN_INFO_ERROR,"数据大屏保存失败");
            }

            if (CollUtil.isEmpty(component.getComponentInfoList())) {
                continue;
            }

            storeComponentList(dataScreenId, component.getComponentInfoList(), componentInfoMap, versionType, user);
        }
    }


    private void insertMainTable(Long dataScreenId,
                                 ComponentInfo componentInfo,
                                 String cardType,
                                 Long cardId,
                                 String versionType,
                                 UserInfo user) {

        //保存组件图层信息
        Long layerId = null;
        if (!cardType.equals(ComponentTypeEnum.TAB.getCode())) {
            ComponentLayerDO componentLayerDO = ComponentLayerDO.builder()
                    .isLock(componentInfo.getIsLock())
                    .isShow(componentInfo.getIsShow())
                    .orderNo(componentInfo.getOrderNo())
                    .resourceId(dataScreenId)
                    .resourceType(DATASCREEN_RESOURCE_TYPE)
                    .createdBy(user.getAccount())
                    .updatedBy(user.getAccount())
                    .build();
            layerId = componentLayerDaoService.insert(componentLayerDO);
        }

        ComponentDO componentDO = ComponentDO.builder()
                .resourceId(dataScreenId)
                .resourceType(DATASCREEN_RESOURCE_TYPE)
                .versionType(versionType)
                .name(componentInfo.getComponentName())
                .location(JSONUtil.toJsonStr(componentInfo.getLocation()))
                .layerId(layerId)
                .cardType(cardType)
                .cardId(cardId)
                .cardUniqueKey(componentInfo.getComponentCode())
                .createdBy(user.getAccount())
                .updatedBy(user.getAccount())
                .build();
        componentDaoService.insert(componentDO);
    }


    private void delete(List<DataScreenDO> dataScreenDOList, String versionType, UserInfo user) {
        List<DataScreenDO> deleteDataScreenList = new ArrayList<>();


        if (dataScreenDOList.isEmpty()) {
            return;
        }

        //如果是保存，则删除草稿版本数据，如果是发布，则删除保存和发布版本数据
        if (versionType.equals(PUBLISHED_STATUS)) {
            deleteDataScreenList = dataScreenDOList;
        } else if (versionType.equals(DRAFT_VERSION_TYPE)) {
            deleteDataScreenList = dataScreenDOList.stream()
                    .filter(dataScreenDO -> !dataScreenDO.getVersionType().equals(PUBLISHED_STATUS)).collect(
                            Collectors.toList());
        }

        for (DataScreenDO dataScreenDO : deleteDataScreenList) {
            deleteDataScreen(dataScreenDO.getId(), user.getAccount());
        }
    }

    private void deleteDataScreen(Long dataScreenId, String account) {
        // 删除数据大屏主表数据和配置表数据
        dataScreenDaoService.delete(dataScreenId, account);
        dataScreenConfDaoService.delete(dataScreenId, account);

        // 获取数据大屏下所有组件主表数据
        List<ComponentDO> componentDOList = componentDaoService.findByCode(dataScreenId, null);

        componentDOList.forEach(componentDO -> {
            // 删除组件主表数据
            componentDaoService.delete(dataScreenId, account);

            // 删除组件图层数据
            componentLayerDaoService.delete(componentDO.getLayerId(), account);

            // 删除组件图层事件数据
            componentLayerEventDaoService.delete(dataScreenId, componentDO.getCardUniqueKey(), account);

            // 删除对应组件详情数据
            deleteComponentDetails(componentDO, account);
        });
    }

    private void deleteComponentDetails(ComponentDO componentDO, String account) {
        ComponentHandler componentStoreHandler = componentHandlerFactory.getComponentStoreHandler(componentDO.getCardType());
        if (componentStoreHandler != null) {
            componentStoreHandler.deleteComponent(componentDO, account);
        }
    }
}


