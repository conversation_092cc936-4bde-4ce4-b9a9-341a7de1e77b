package com.bestpay.bigdata.bi.report.schedule.sender.bean;

import com.bestpay.bigdata.bi.report.schedule.subscribe.bean.SubscribeContent;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-03-07-13:27
 */
@Builder
@Data
public class EmailSubscribeContent extends SubscribeContent {

    public EmailSubscribeContent(List<String> filePathList) {
        super(filePathList);
    }

    public EmailSubscribeContent() {
    }
}
