package com.bestpay.bigdata.bi.report.service.impl.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dashboard.IndexCardQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DataSet;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.report.ColumnPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.DataSetFieldTypeEnum;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.ReportErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardIndexTextCardService;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
import com.bestpay.bigdata.bi.report.request.tablecard.IndexCardListRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.IndexCardRequest;
import com.bestpay.bigdata.bi.report.response.dashboard.IndexCardVO;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.service.impl.dashboard.DashboardTableCardServiceImpl;
import com.bestpay.bigdata.bi.report.service.report.EmbedIndexCardService;
import com.bestpay.bigdata.bi.report.util.EngineQueryParamCheckUtil;
import com.bestpay.bigdata.bi.report.util.ReportDateUtil;
import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: bj
 * @date: 2022/07/27
 */
@Slf4j
@Service
public class EmbedIndexCardServiceImpl implements EmbedIndexCardService {

    @Resource
    private DashboardIndexTextCardService indexTextCardService;
    @Resource
    private DatasetService datasetService;
    @Resource
    private DatePickerDAOService pickerDAOService;

    @Resource
    private ReportDateUtil reportDateUtil;

    @Resource
    private DashboardDaoService dashboardDaoService;

    @Resource
    private EngineQueryParamCheckUtil engineQueryParamCheckUtil;
    /**
     * 新增/更新/删除指标
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response<Long> addOrUpdateIndexCard(IndexCardRequest request){
        /**
         * 直接删除后返回
         */
        // 如果是delete操作不用校验数据
        if((request.getStatusCode()!=null
            &&StatusCodeEnum.DELETE.getCode()==request.getStatusCode())) {
            DashboardIndexTextCardDO textCardDO = new DashboardIndexTextCardDO();
            BeanUtils.copyProperties(request, textCardDO);
            Long cardId = indexTextCardService.update(textCardDO);
            return Response.ok(cardId);
        }

        /**
         * 下面是insert 或者 更新操作
         */
        // 参数校验
        checkParam(request);

        DashboardIndexTextCardDO textCardDO = new DashboardIndexTextCardDO();
        BeanUtils.copyProperties(request, textCardDO);
        String indexInfoJson = JSONUtil.toJsonStr(request.getIndexInfo());
        textCardDO.setIndexInfo(indexInfoJson);
        textCardDO.setDragResult(JSONUtil.toJsonStr(request.getDragResult()));
        textCardDO.setCountFiledList(JSONUtil.toJsonStr(request.getCountFiledList()));

        UserInfo user = UserContextUtil.getUserInfo();
        textCardDO.setUpdatedBy(user.getAccount());
        textCardDO.setCreatedBy(user.getAccount());

        // 过滤
        textCardDO.setFilterdragResult(JSONUtil.toJsonStr(getFilterColumn(request)));
        JSONObject jsonObject = JSONUtil.parseObj(indexInfoJson);
        long dataSetId = Long.parseLong(String.valueOf(jsonObject.get("dataSet")));
        textCardDO.setDatasetId(dataSetId);
        // update
        Long cardId;
        if(request.getId()!=null){
            cardId = indexTextCardService.update(textCardDO);
        }else{
            cardId = indexTextCardService.insert(textCardDO);
        }

        return Response.ok(cardId);
    }

    private void checkParam(IndexCardRequest request) {

        // 校验引用指标是否重复
        IndexCardQueryDTO find = new IndexCardQueryDTO();
        BeanUtils.copyProperties(request, find);

        List<DashboardIndexTextCardDO> indexList = indexTextCardService.find(find);
        if(CollUtil.isNotEmpty(indexList)){
            for (DashboardIndexTextCardDO existEmbedIndex : indexList) {

                JSONObject indexInfo
                    = (JSONObject) JSONUtil.parse(existEmbedIndex.getIndexInfo());

                JSONObject newIndexInfo
                    = (JSONObject) JSONUtil.parse(request.getIndexInfo());

                if(newIndexInfo!=null &&  newIndexInfo.getStr("name")!=null
                    && newIndexInfo.getStr("name").equals(indexInfo.getStr("name"))
                    && !existEmbedIndex.getId().equals(request.getId())){
                    throw new BiException(ReportErrorCode.REPORT_INDEX_NAME_EXISTS,"已存在相同名称的引用指标");
                }

            }
        }
        final Long dashboardId = request.getDashboardId();
        // TODO   重复代码优化  也就是下面的这些校验
        Dashboard dashboard = dashboardDaoService.getById(dashboardId);
        Boolean check = reportDateUtil.isCheck(Date.from(dashboard.getCreatedAt()));
        if (check){
            JSONObject object = new JSONObject(request.getIndexInfo());
            Long datasetId  = Long.parseLong(String.valueOf(object.get("dataSet")));
            List<DatasetColumnConfigDTO> columnConfigList = engineQueryParamCheckUtil.getColumnConfigList(datasetId);
            Map<String, DatasetColumnConfigDTO> columnEnNameMap = columnConfigList.stream().collect(Collectors.toMap(DatasetColumnConfigDTO::getEnName, Function.identity()));
            Map<String, DatasetColumnConfigDTO> columnOriginEnNameMap = columnConfigList.stream().filter(c-> StrUtil.isNotBlank(c.getOriginEnName())).collect(Collectors.toMap(DatasetColumnConfigDTO::getOriginEnName, Function.identity()));
            List<ComputeComponentPropertyDTO> countFiledList = request.getCountFiledList();
            for (ComputeComponentPropertyDTO columnProperty : countFiledList) {
                DatasetColumnConfigDTO dto = new DatasetColumnConfigDTO();
                dto.setEnName(columnProperty.getEnName());
                dto.setShowTypeName(columnProperty.getShowTypeName());
                String fieldType = columnProperty.getFieldType();
                DataSetFieldTypeEnum dataSetFieldTypeEnum = DataSetFieldTypeEnum.getNameByCode(fieldType);
                dto.setFieldType(dataSetFieldTypeEnum);
                columnEnNameMap.put(columnProperty.getEnName(),dto);
            }
            DashboardTableCardServiceImpl.checkIndexTextProperty(request,columnEnNameMap,columnOriginEnNameMap);
        }
    }

    private List<ColumnPropertyDTO> getFilterColumn(IndexCardRequest request) {
        List<ColumnPropertyDTO> columnPropertyDTOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(request.getFilterdragResult())) {
            return Lists.newArrayList();
        }

        for (FilterComponentPropertyDTO column : request.getFilterdragResult()) {
            ColumnPropertyDTO columnPropertyDTO = new ColumnPropertyDTO();
            BeanUtils.copyProperties(column, columnPropertyDTO);

            DatePickerConfigDO picker = new DatePickerConfigDO();
            BeanUtils.copyProperties(column, picker);

            if (FieldType.DATETIME.name().equalsIgnoreCase(column.getShowTypeName())) {
                picker.setDefaultValue(JSONUtil.toJsonStr(column.getDefaultValues()));
                Long pickerId = pickerDAOService.insert(picker);
                columnPropertyDTO.setDatePickerId(pickerId);
            }

            columnPropertyDTOS.add(columnPropertyDTO);
        }

        return columnPropertyDTOS;
    }

    @Override
    public Response<IndexCardVO> indexCardDetail(Long id) {
        IndexCardQueryDTO find = new IndexCardQueryDTO();
        find.setIdList(Lists.newArrayList(id));

        List<DashboardIndexTextCardDO> indexList = indexTextCardService.find(find);

        IndexCardVO cardVO = new IndexCardVO();
        DashboardIndexTextCardDO cardDO = indexList.get(0);
        cardDOtoCardVO(cardDO, cardVO);

        log.info("indexCardDetail, cardVO={}", JSONUtil.toJsonStr(cardVO));

        return Response.ok(cardVO);
    }

    @Override
    public Response<List<IndexCardVO>> indexCardList(IndexCardListRequest request) {

        IndexCardQueryDTO find = new IndexCardQueryDTO();
        BeanUtils.copyProperties(request, find);

        List<DashboardIndexTextCardDO> indexList = indexTextCardService.find(find);
        List<IndexCardVO> cardVOS = Lists.newArrayList();

        for (DashboardIndexTextCardDO cardDO : indexList) {
            IndexCardVO cardVO = new IndexCardVO();
            cardDOtoCardVO(cardDO, cardVO);
            cardVOS.add(cardVO);
        }

        log.info("indexCardDetail, cardVOS={}", JSONUtil.toJsonStr(cardVOS));

        return Response.ok(cardVOS);
    }

    private void cardDOtoCardVO(DashboardIndexTextCardDO cardDO, IndexCardVO cardVO) {
        BeanUtils.copyProperties(cardDO, cardVO);

        // 数据集信息
        Long datasetId = JSONUtil.parseObj(cardDO.getIndexInfo()).getLong("dataSet");
        List<DataSet> dataSets = datasetService.getDataSet(Lists.newArrayList(datasetId));

        cardVO.setDatasetInfo(dataSets.get(0));
        cardVO.setIndexInfo(JSONUtil.parse(cardDO.getIndexInfo()));

        // 指标
        cardVO.setDragResult(JSONUtil.toList(cardDO.getFilterdragResult(), IndexComponentPropertyDTO.class));

        // 过滤
        cardVO.setFilterdragResult(getFilterColumnResponse(cardDO));

        // 计算字段
        cardVO.setCountFiledList(JSONUtil.toList(cardDO.getFilterdragResult(), ComputeComponentPropertyDTO.class));
    }

    private List<FilterComponentPropertyDTO> getFilterColumnResponse(DashboardIndexTextCardDO cardDO) {

        List<ColumnPropertyDTO> columnPropertyDtos
        = JSONUtil.toList(cardDO.getFilterdragResult(), ColumnPropertyDTO.class);

        List<FilterComponentPropertyDTO> responses = Lists.newArrayList();
        for (ColumnPropertyDTO columnPropertyDto : columnPropertyDtos) {
            FilterComponentPropertyDTO response = new FilterComponentPropertyDTO();
            BeanUtils.copyProperties(columnPropertyDto, response);
            responses.add(response);

            // 获取日期配置
            if(FieldType.DATETIME.name().equalsIgnoreCase(columnPropertyDto.getShowTypeName())){
                if(columnPropertyDto.getDatePickerId()!=null){

                    DatePickerConfigDO configDO
                        = pickerDAOService.select(columnPropertyDto.getDatePickerId());

                    BeanUtils.copyProperties(configDO, response);
                    response.setDefaultValues(JSONUtil.toList(configDO.getDefaultValue(), String.class));
                }
            }
        }

        return responses;
    }
}
