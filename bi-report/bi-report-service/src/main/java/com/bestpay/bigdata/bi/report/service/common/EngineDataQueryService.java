package com.bestpay.bigdata.bi.report.service.common;

import com.bestpay.bigdata.bi.common.dto.dashboard.DashBoardRelatedDTO;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;

/**
 * <AUTHOR>
 */
public interface EngineDataQueryService {

  Response<QueryIndexAndReportResponse> getData(ReportRequest oldReport,
      Boolean isCheck,
      DashBoardRelatedDTO dto);

  Long getCount(ReportRequest oldReport, UserInfo userInfo);
}
