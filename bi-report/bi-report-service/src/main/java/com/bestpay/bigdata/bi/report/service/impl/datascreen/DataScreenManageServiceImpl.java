package com.bestpay.bigdata.bi.report.service.impl.datascreen;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.bean.UserInfoRequest;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.DataScreenStatusEnum;
import com.bestpay.bigdata.bi.common.enums.PlatformEnum;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.datascreen.DataScreenDaoService;
import com.bestpay.bigdata.bi.database.dao.datascreen.DataScreenDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.DataScreenMainDO;
import com.bestpay.bigdata.bi.report.enums.embed.AppEmbedTypeEnum;
import com.bestpay.bigdata.bi.report.enums.embed.NetworkTypeEnum;
import com.bestpay.bigdata.bi.report.request.datascreen.DataScreenPreviewRequest;
import com.bestpay.bigdata.bi.report.request.datascreen.DataScreenRequest;
import com.bestpay.bigdata.bi.report.request.datascreen.DataScreenStatusRequest;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenPartakeRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenQueryRequest;
import com.bestpay.bigdata.bi.report.response.datascreen.DataScreenListVO;
import com.bestpay.bigdata.bi.report.response.datascreen.DataScreenVO;
import com.bestpay.bigdata.bi.report.service.datascreen.DataScreenMainService;
import com.bestpay.bigdata.bi.report.service.datascreen.DataScreenManageService;
import com.bestpay.bigdata.bi.report.service.datascreen.DataScreenService;
import com.bestpay.bigdata.bi.report.service.embed.DataScreenAppEmbedService;
import com.bestpay.bigdata.bi.report.service.impl.DataScreenTransactionalService;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Preconditions;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class DataScreenManageServiceImpl implements DataScreenManageService {

    @Resource
    private AiplusService aiplusService;

    @Resource
    private AiPlusUserService aiPlusUserService;

    @Resource
    private DataScreenService dataScreenService;

    @Resource
    private DataScreenDaoService dataScreenDaoService;

    @Resource
    private DataScreenMainService dataScreenMainService;

    @Resource
    private DataScreenTransactionalService dataScreenTransactionalService;

    @Resource
    private DataScreenAppEmbedService dataScreenAppEmbedService;

    @Override
    public PageQueryVO<DataScreenListVO> list(DataScreenRequest request) {

        PageQueryVO<DataScreenListVO> screenListVOPageQueryVO = new PageQueryVO<>();

        // 1：构建查询条件 查询 数据大屏uuid
        List<String> distinctDataScreenUuidList = dataScreenDaoService.queryDistinctDataScreenUuid(buildQueryParam(request));

        if (CollUtil.isEmpty(distinctDataScreenUuidList)){
            screenListVOPageQueryVO.setTotalSize(0L);
            screenListVOPageQueryVO.setData(new ArrayList<>());
            return screenListVOPageQueryVO;
        }
        Page<DataScreenListVO> page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        // 2: 查询 大屏主表  用于分页
        List<DataScreenMainDO> dataScreenStatusDOList = dataScreenMainService.queryByDataScreenUuidList(distinctDataScreenUuidList);

        screenListVOPageQueryVO.setTotalSize(page.getTotal());

        if (CollUtil.isNotEmpty(dataScreenStatusDOList)){
            List<String> dsUuidList = dataScreenStatusDOList.stream()
                .map(DataScreenMainDO::getDataScreenUuid)
                .distinct()
                .collect(Collectors.toList());

            // 3: 再去查询详细信息
            List<DataScreenDO> dataScreenDOList = dataScreenDaoService.queryByDsUuidList(dsUuidList);
            if (CollUtil.isNotEmpty(dataScreenDOList)){
                List<DataScreenListVO> voList = new ArrayList<>(dataScreenDOList.size());
                // 先进行分组  再进行排序  分组后取第一个  第一个是最新的
                Map<String, List<DataScreenDO>> dataScreenGroup = dataScreenDOList.stream().collect(Collectors.groupingBy(DataScreenDO::getDataScreenUuid));
                List<UserInfo> userList = aiPlusUserService.getUserList(new UserInfoRequest());
                Map<String, UserInfo> userInfoMap = userList.stream().collect(Collectors.toMap(UserInfo::getEmail, Function.identity(),(k1,k2)->k1));
                List<Org> orgList = aiplusService.getOrgList();
                Map<String, String> orgInfoMap = orgList.stream().collect(Collectors.toMap(Org::getCode, Org::getName));

                for (DataScreenMainDO dataScreenStatusDO : dataScreenStatusDOList) {
                    Optional.of( dataScreenGroup.get(dataScreenStatusDO.getDataScreenUuid()))
                            .ifPresent(ds-> voList.add(buildDataScreenListVO(dataScreenStatusDO,ds.get(0),userInfoMap,orgInfoMap)));
                }
                screenListVOPageQueryVO.setData(voList);
            }
        }
        return screenListVOPageQueryVO;
    }

    @Override
    public Integer updateStatus(DataScreenStatusRequest request) {
        Long dataScreenId = request.getDataScreenId();
        String status = request.getStatus();
        DataScreenMainDO dataScreenMainDO = dataScreenMainService.queryById(dataScreenId);
        Preconditions.checkArgument(Objects.nonNull(dataScreenMainDO),"数据大屏不存在");
        String dataScreenUuid = dataScreenMainDO.getDataScreenUuid();
        List<String> dataScreenUuidList = Stream.of(dataScreenUuid).collect(Collectors.toList());
        List<DataScreenDO> dataScreenDOList = dataScreenDaoService.queryByDsUuidList(dataScreenUuidList);
        Preconditions.checkArgument(CollUtil.isNotEmpty(dataScreenDOList),"数据大屏信息不存在");
        dataScreenTransactionalService.updateStatus(dataScreenUuidList,dataScreenId,status, UserContextUtil.getUserInfo());
        return 1;

    }


    @Override
    @Transactional(rollbackFor = {Exception.class, BusinessException.class})
    public String share(DataScreenPartakeRequest request) {
        UserInfo user = UserContextUtil.getUserInfo();

        // 状态校验
        DataScreenMainDO main = dataScreenMainService.queryById(request.getDatsScreenId());

        // 获取已发布版本信息
        DataScreenQueryRequest dataScreenQueryRequest = new DataScreenQueryRequest();
        dataScreenQueryRequest.setDataScreenUUID(main.getDataScreenUuid());
        dataScreenQueryRequest.setVersionType(DataScreenStatusEnum.PUBLISHED.getCode());

        DataScreenVO dataScreenVO =  dataScreenService.queryDataScreenInfo(dataScreenQueryRequest);

        Preconditions.checkArgument(DataScreenStatusEnum.PUBLISHED.getCode().equals(main.getStatus()),"数据大屏不是已发布状态，不能分享");

        // 生成应用嵌入token
        Response result = saveAppEmbed(main.getId(),dataScreenVO.getName(),user);

        return result.getData().toString();
    }

    private Response<String> saveAppEmbed(Long dataScreenMainsId,
        String name,
        UserInfo user) {

        AppEmbedRequest appEmbedRequest = AppEmbedRequest.builder()
            .embedObjectId(dataScreenMainsId)
            .embedObjectName(name)
            .ownerName(user.getName())
            .email(user.getEmail())
            .orgCode(user.getOrg().getCode())
            .embedType(AppEmbedTypeEnum.NEW_DATASCREEN.getCode())
            .isPublishMobile(0)
            .networkType(NetworkTypeEnum.INSIDE.getCode())
            .platform(PlatformEnum.BI.getCode()).versionType(DataScreenStatusEnum.PUBLISHED.getCode())
            .taskName(name)  //todo 拼接任务名称
            .build();

        return dataScreenAppEmbedService.completeAppEmbed(appEmbedRequest);
    }


    @Override
    public Boolean publish(DataScreenPreviewRequest request) {
        DataScreenMainDO dataScreenMainDO = dataScreenMainService.queryById(request.getDataScreenId());
        Preconditions.checkArgument(Objects.nonNull(dataScreenMainDO) ,"数据大屏不存在!");
        List<String> dataScreenUuidList = Stream.of(dataScreenMainDO.getDataScreenUuid()).collect(Collectors.toList());
        Preconditions.checkArgument(!DataScreenStatusEnum.PUBLISHED.getCode().equals(dataScreenMainDO.getStatus()) ,"该数据大屏状态信息已经是已发布状态，请勿重复操作");
        // 此处查出来最多只有两个数据   如果是两个 必定是saved 和 published
        List<DataScreenDO> dataScreenDOList = dataScreenDaoService.queryByDsUuidList(dataScreenUuidList);
        Preconditions.checkArgument(CollUtil.isNotEmpty(dataScreenDOList),"大屏信息不存在");

        Long toUpdateId = null;
        Long toDeleteId = null;
        for (DataScreenDO screenDO : dataScreenDOList) {
            Long id = screenDO.getId();
            if ( DataScreenStatusEnum.PUBLISHED.getCode().equals(screenDO.getVersionType())){
                toDeleteId = id;
            }else {
                toUpdateId = id;
            }
        }
        dataScreenTransactionalService.listPublish(dataScreenMainDO.getId(),DataScreenStatusEnum.PUBLISHED,toUpdateId,toDeleteId);
        return Boolean.TRUE;
    }

    @Override
    public DataScreenMainDO queryMainDSById(Long datsScreenId) {
        return dataScreenMainService.queryMainDSById(datsScreenId);
    }

    /**
     * 构建查询条件
     * @param request 入参
     * @return 查询条件
     */
    private static @NotNull DataScreenDO buildQueryParam(DataScreenRequest request) {
        DataScreenDO dataScreenDO = new DataScreenDO();
        dataScreenDO.setName(request.getName());
        dataScreenDO.setOwnerEmail(request.getOwnerEmail());
        dataScreenDO.setOrgCode(request.getOrgCode());
        return dataScreenDO;
    }


    /**
     * 构建返回给前端的参数
     * @param dataScreenStatusDO 大屏主表信息
     * @param screenDO  大屏信息
     * @return 返回给前端的参数
     */
    public static DataScreenListVO buildDataScreenListVO(DataScreenMainDO dataScreenStatusDO,
                                                         DataScreenDO screenDO,
                                                         Map<String, UserInfo> userInfoMap,
                                                         Map<String, String> orgInfoMap ){
        DataScreenStatusEnum byCode = DataScreenStatusEnum.getByCode(dataScreenStatusDO.getStatus());
        UserInfo userInfo = userInfoMap.get(screenDO.getOwnerEmail());
        String orgName = orgInfoMap.get(screenDO.getOrgCode());
        return DataScreenListVO
                .builder()
                .id(dataScreenStatusDO.getId())
                .name(screenDO.getName())
                .dataScreenUUID(screenDO.getDataScreenUuid())
                .status(dataScreenStatusDO.getStatus())
                .statusDesc(Objects.nonNull(byCode)?byCode.getMessage() : StrUtil.EMPTY)
                .previewUrl(screenDO.getPreviewUrl())
                .owner(Objects.nonNull(userInfo)?userInfo.getAccount() : StrUtil.EMPTY)
                .ownerEmail(screenDO.getOwnerEmail())
                .orgName(StrUtil.isNotBlank(orgName)?orgName:StrUtil.EMPTY)
                .createdAt(dataScreenStatusDO.getCreatedAt())
                .updatedAt(dataScreenStatusDO.getUpdatedAt())
                .versionType(screenDO.getVersionType())
                .build();
    }
}
