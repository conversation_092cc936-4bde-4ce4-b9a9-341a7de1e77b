package com.bestpay.bigdata.bi.report.beforeSQL.picker.fixdate.day;

import com.bestpay.bigdata.bi.report.beforeSQL.enums.FixDateTypeEnum;
import com.bestpay.bigdata.bi.report.beforeSQL.picker.fixdate.FixDateTypeHandler;
import com.google.common.collect.Lists;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MondayHandler implements FixDateTypeHandler {

  /**
   * 获取精确时间
   * @return
   */
  @Override
  public List<String> getTime(){
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    List<String> times = Lists.newArrayList();
    LocalDate now = LocalDate.now();

    LocalDate monday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    String startTime = monday.format(formatter) + " 00:00:00";
    String endTime = monday.format(formatter) + " 23:59:59";

    times.add(startTime);
    times.add(endTime);

    return times;
  }

  /**
   * 筛选器类型
   * @return
   */
  @Override
  public FixDateTypeEnum dateType(){
    return FixDateTypeEnum.MONDAY;
  }

  public static void main(String[] args){
    MondayHandler mondayHandler = new MondayHandler();
    System.out.println(mondayHandler.getTime());
  }

}
