package com.bestpay.bigdata.bi.report.request.new_datascreen;

import com.bestpay.bigdata.bi.common.dto.datascreen.ComponentInfo;
import com.bestpay.bigdata.bi.common.dto.datascreen.DataScreenInfo;
import com.bestpay.bigdata.bi.common.dto.datascreen.EmbedInfoDTO;
import lombok.Data;

import java.util.List;

@Data
public class DataScreenPreviewRequest {

    private String dataScreenUUID;
    private DataScreenInfo dataScreenInfo;
    private List<ComponentInfo> data;

    /**
     * 嵌入相关信息
     */
    private EmbedInfoDTO embedInfoDTO;
    /*
    {
	"isPublishMobile": 0,
	"networkType": 0,
	"embedObjectId": 1,
    "embedType":"new_datascreen",
    "embedObjectName":"数据大屏名称",
    "platform":"BI"
}
     */
}
