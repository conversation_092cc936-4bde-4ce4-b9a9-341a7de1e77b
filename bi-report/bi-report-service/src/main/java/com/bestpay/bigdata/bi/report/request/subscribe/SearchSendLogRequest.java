package com.bestpay.bigdata.bi.report.request.subscribe;

import com.bestpay.bigdata.bi.common.bean.PageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订阅列表搜索
 */
@EqualsAndHashCode(callSuper = true)
@Data
 @ApiModel(description = "订阅列表搜索")
public class SearchSendLogRequest extends PageVo {

    @ApiModelProperty(value = "责任人邮箱")
    private String ownerEmail;

    @ApiModelProperty(value = "订阅状态 0：成功 ,1:失败,2:超时")
    private String status;

    @ApiModelProperty(value = "订阅类型")
    private String objectType;

    @ApiModelProperty(value = "接受方式")
    private Integer subscribeType;

    @ApiModelProperty(value = "订阅对象ID")
    private Long objectId;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "关键字")
    private String keyword;


}
