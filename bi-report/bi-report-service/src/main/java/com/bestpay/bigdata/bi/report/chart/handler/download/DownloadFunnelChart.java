package com.bestpay.bigdata.bi.report.chart.handler.download;

import com.bestpay.bigdata.bi.backend.api.QueryService;
import com.bestpay.bigdata.bi.common.entity.ColumnName;
import com.bestpay.bigdata.bi.report.chart.bean.ChartContext;
import com.bestpay.bigdata.bi.report.chart.bean.ChartHeader;
import com.bestpay.bigdata.bi.report.chart.bean.ReportBaseModel;
import com.bestpay.bigdata.bi.report.chart.handler.query.FunnelChart;
import com.bestpay.bigdata.bi.report.service.common.EngineDataQueryService;
import com.google.common.collect.Lists;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DownloadFunnelChart extends FunnelChart {

  public DownloadFunnelChart(ChartContext context,
      QueryService queryService,
      EngineDataQueryService dataQueryService) {
    super(context, queryService, dataQueryService);
  }

  @Override
  public ChartHeader getChartHeader() {
    ChartHeader chartHeader = new ChartHeader();
    List<ColumnName> header = Lists.newArrayList();

    // 1维度1指标的情况
    ReportBaseModel reportBaseModel = context.getReportBaseModel();
    if(reportBaseModel.getShowColumnList().size() > 0 && reportBaseModel.getIndexColumnList().size() ==1){
      header.add(ColumnName.builder().label(reportBaseModel
          .getShowColumnList().get(0).getName()).prop("name").build());
      header.add(ColumnName.builder().label(reportBaseModel
          .getIndexColumnList().get(0).getName()).prop("formatValue").build());
    }

    // 0维度N指标的情况
    if(reportBaseModel.getShowColumnList().size() ==0 && reportBaseModel.getIndexColumnList().size() > 0){
      header.add(ColumnName.builder().label("指标名称").prop("name").build());
      header.add(ColumnName.builder().label("指标值").prop("formatValue").build());
    }

    // 转化率
    header.add(ColumnName.builder().label("上步转换率").prop("previousConversionRate").build());
    header.add(ColumnName.builder().label("整体转化率").prop("overallConversionRate").build());

    chartHeader.setHeader(header);
    return chartHeader;
  }

}
