server:
  port: 8086
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
    min-response-size: 2048
app:
  id:
    bigdata-bi
apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
    namespaces: application,auth-config.yml,dubbo,logging,redis.yml,mysql,dataSource,dynamic_refresh

dubbo:
  application:
    name: bi-report
  protocol:
    port: 20996
  scan:
    base-packages: com.bestpay.bigdata.bi.report.api.impl
  registry:
    simplified: true

logging:
  config: classpath:logback-report.xml
spring:
  application:
    name: report
