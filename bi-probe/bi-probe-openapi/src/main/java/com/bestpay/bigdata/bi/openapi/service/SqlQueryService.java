package com.bestpay.bigdata.bi.openapi.service;

/**
 * <AUTHOR>
 * @Date 2022/9/7
 */

import cn.hutool.core.collection.CollUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.bestpay.bigdata.bi.backend.api.QueryService;
import com.bestpay.bigdata.bi.common.api.QueryRedisService;
import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.common.ResultTypeEnum;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.common.OpenApiProperties;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.*;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.oss.OssService;
import com.bestpay.bigdata.bi.common.oss.OssServiceFactory;
import com.bestpay.bigdata.bi.common.oss.wrap.MiniIOOssContext;
import com.bestpay.bigdata.bi.common.request.InsertByCsvRequest;
import com.bestpay.bigdata.bi.common.request.InsertByPathRequest;
import com.bestpay.bigdata.bi.common.request.TextQueryRequest;
import com.bestpay.bigdata.bi.common.request.UploadRequest;
import com.bestpay.bigdata.bi.common.response.UploadResult;
import com.bestpay.bigdata.bi.common.util.DownloadFileUtil;
import com.bestpay.bigdata.bi.common.util.FastDFSPoolUtil;
import com.bestpay.bigdata.bi.common.util.MinioUtil;
import com.bestpay.bigdata.bi.openapi.access.AccessControlManager;
import com.bestpay.bigdata.bi.openapi.bean.SessionContext;
import com.bestpay.bigdata.bi.openapi.query.SessionContextSupplier;
import com.bestpay.bigdata.bi.openapi.request.DownloadRequest;
import com.bestpay.bigdata.bi.openapi.request.UploadFileRequest;
import com.bestpay.bigdata.bi.openapi.vo.QueryResult;
import com.bestpay.bigdata.bi.openapi.vo.QueryStatus;
import com.bestpay.bigdata.bi.probe.enums.DataBaseSource;
import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class SqlQueryService {

  private static final Logger logger = LoggerFactory.getLogger(SqlQueryService.class);

  @Reference(version = "1.0.0")
  private QueryService queryService;

  @Autowired
  private QueryRedisService queryRedisService;

  @Autowired
  private ApolloRefreshConfig apolloRefreshConfig;

  @Autowired
  private MinioUtil minioUtil;

  @Value("${dfs.tracker}")
  private String TRACKER_SERVER;

  @Resource
  private AccessControlManager accessControlManager;

  @Resource
  private SessionContextSupplier sessionContextSupplier;

  @Resource
  private OssServiceFactory ossServiceFactory;

  @Resource
  private AiPlusUserService aiPlusUserService;


  public String singleQuery(TextQueryRequest request) {
    SessionContext sessionContext = sessionContextSupplier.createSessionContext(request);
    accessControlManager.checkAccessControl(sessionContext, request.getSql());

    logger.info("{} permission verification passed", request.getUsername());
    return doSingleSqlExecute(request, Constant.SQL_DQL_TYPE, request.getSql());
  }

  public QueryResult syncSingleQuery(TextQueryRequest request) {
    SessionContext sessionContext = sessionContextSupplier.createSessionContext(request);
    accessControlManager.checkAccessControl(sessionContext, request.getSql());

    logger.info("{} permission verification passed", request.getUsername());
    return doSyncTextQuery(request, Constant.SQL_DQL_TYPE, request.getSql());
  }

  public String batchExecute(TextQueryRequest request) {
    SessionContext sessionContext = sessionContextSupplier.createSessionContext(request);
    accessControlManager.checkAccessControl(sessionContext, request.getSql());

    String[] batchSql = request.getSql().split(";");
    logger.info("{} permission verification passed", request.getUsername());
    return doSqlExecute(request, Constant.SQL_NONE_DQL_TYPE,Arrays.asList(batchSql));
  }


  public QueryStatus queryStatus(String queryId) {
    Integer queryStatus = queryRedisService.getQueryStatus(queryId);
    logger.info("queryId : {} status is {}", queryId, queryStatus);
    Status status = Status.getStatus(queryStatus);
    QueryStatus statusVo = new QueryStatus();
    if (Status.QUERY_FAILED.equals(status)) {
      QueryContext queryResult = queryRedisService.getQueryResult(queryId);
      statusVo.setStatus(status.name());
      statusVo.setMessage(queryResult.getMessage());
      return statusVo;
    }

    if (Status.QUERY_EXECUTING.equals(status)) {
      QueryContext queryResult = queryRedisService.getQueryResult(queryId);
      long now = System.currentTimeMillis();
      Long queryStartTime = queryResult.getQueryStartMillis();
      long interval = now - queryStartTime;
      Duration duration = Duration.ofMillis(interval);
      if (duration.getSeconds() > apolloRefreshConfig.getQueryTimeoutSecond()) {
        logger.warn("the queryTask running beyond {}'s ,that is execute timeout,queryId:{}", duration.getSeconds(),
            queryId);
        statusVo.setStatus(Status.QUERY_TIMEOUT.name());
        return statusVo;
      }
    }

    statusVo.setStatus(status.name());
    return statusVo;
  }

  public QueryResult queryResult(String queryId,Integer skip,Integer limit) {
    QueryContext queryResult = queryRedisService.getQueryResult(queryId);
    logger.info("queryId:{} get queryContext from backend,data size : {}", queryId, queryResult.getResultRowsNum());
    List<List<String>> list = queryResult.getResults();
    String[] columnNames = queryResult.getColumnNames();
    double queryTimeSecond = queryResult.getQueryTimeSecond();
    long resultSize = list == null ? 0 : list.size();
    if (!CollectionUtils.isEmpty(list)) {
      list = list.stream().skip(skip).limit(limit).collect(Collectors.toList());
    }
    logger.info("query result info, columns:{}, queryTimeSecond:{}", columnNames, queryTimeSecond);
    logger.info("Final state of query [{}] is: {}, elapse time: {}s", queryResult.getQueryId(), queryResult.getStatus(),
        queryResult.getQueryTimeSecond());
    QueryResult result = new QueryResult();
    if (Objects.nonNull(queryResult.getResultType())
            && ResultTypeEnum.PATH.getCode().equals(queryResult.getResultType())) {
      result.setData(queryResult.getResultPath());
    } else {
      result.setData(list);
      result.setTotal(queryResult.getResultRowsNum());
    }
    result.setTotal(resultSize);
    result.setQueryTimeInSecond(queryTimeSecond);
    result.setColumnNames(columnNames==null?null:Arrays.asList(columnNames));
    return result;
  }

  public Boolean cancelQuery(String queryId,String targetUser) {
    QueryContext queryResult = queryRedisService.getQueryResult(queryId);
    String username = queryResult.getUsername();
    if (!targetUser.equals(username)) {
      throw new IllegalArgumentException("取消查询失败，你没有该任务取消执行权限");
    }

    return queryService.executeCancel(queryId);
  }

  private String doSqlExecute(TextQueryRequest request,Integer sqlType,List<String> batchSql) {
    QueryContext queryContext = new QueryContext();
    queryContext.setRouterGroup(RouterGroupEnum.BATCH.code());
    queryContext.setBatchSql(batchSql);
    queryContext.setUsername(request.getUsername());
    queryContext.setEngineName(request.getSqlEngine()!=null? request.getSqlEngine() : SQLEngine.KYUUBI.getEngineName());
    queryContext.setTypeCode(QueryType.OPEN_API.getCode());
    queryContext.setDataSource(request.getDatabaseSource()!=null? request.getDatabaseSource()
                                : DataBaseSource.HIVE_11.getName());

    DataBaseSource dataBaseSourceByName = DataBaseSource.getDataBaseSourceByName(request.getDatabaseSource());
    if (dataBaseSourceByName == null){
      queryContext.setClusterName(DataBaseSource.HIVE_11.getName());
    }else {
      queryContext.setClusterName(request.getDatabaseSource());
    }

    queryContext.setSource(request.getSource());
    queryContext.setDatasourceType(request.getDatasourceType());
    queryContext.setQueryWithTimeout(false);
    queryContext.setSqlType(sqlType);
    queryContext.setRequestSystem(apolloRefreshConfig.getRequestSystemSet().contains(request.getRequestSystem()) ? request.getRequestSystem() : null);

    // set cache type
    if (request.getProperties() != null) {
      queryContext.setResultType(OpenApiProperties.getResultType(request.getProperties()));
    }
    //处理用户组织信息并设置到查询上下文中
    handleOrgInfo(request.getUsername(), queryContext);
    QueryContext resContext = queryService.executeQuery(queryContext);
    return resContext.getQueryId();
  }


  /**
   * 处理用户组织信息并设置到查询上下文中
   * @param email 用户邮箱，用于查询用户信息
   * @param queryContext 查询上下文对象，用于存储用户组织信息
   */
  public void handleOrgInfo(String email, QueryContext queryContext){
    AiPlusUserSearchRequest request = new AiPlusUserSearchRequest();
    request.setOwnerNames(Lists.newArrayList(email));
    queryContext.setUserOrg("未查询到组织信息");
    try {
      List<UserInfo> userInfos = aiPlusUserService.getUserList(request);
      if (CollUtil.isNotEmpty(userInfos)){
        queryContext.setUserOrg(userInfos.get(0).getOrg().getName());
      }
    }catch (Exception e ){
      logger.error("调用智加查询用户组织信息异常",e);
    }
  }

  private String doSingleSqlExecute(TextQueryRequest request,Integer sqlType,String sql) {
    QueryContext queryContext = new QueryContext();
    queryContext.setSource(request.getSource());
    queryContext.setRouterGroup(RouterGroupEnum.ADHOC.code());
    queryContext.setSql(sql);
    queryContext.setUsername(request.getUsername());
      // TODO: 2023/7/11  属于必填校验，在controller做参数校验，不应该设置默认值
    queryContext.setEngineName(request.getSqlEngine()!=null? request.getSqlEngine() : SQLEngine.KYUUBI.getEngineName());
    queryContext.setTypeCode(QueryType.OPEN_API.getCode());
      // TODO: 2023/7/11  属于必填校验，在controller做参数校验，不应该设置默认值
    queryContext.setDataSource(request.getDatabaseSource()!=null? request.getDatabaseSource()
            : DataBaseSource.HIVE_11.getName());

    DataBaseSource dataBaseSourceByName = DataBaseSource.getDataBaseSourceByName(request.getDatabaseSource());
      // TODO: 2023/7/11  属于必填校验，在controller做参数校验，不应该设置默认值
    if (dataBaseSourceByName == null){
      queryContext.setClusterName(DataBaseSource.HIVE_11.getName());
    }else {
      queryContext.setClusterName(request.getDatabaseSource());
    }

    queryContext.setQueryWithTimeout(false);
    queryContext.setSqlType(sqlType);
    queryContext.setRequestSystem(apolloRefreshConfig.getRequestSystemSet().contains(request.getRequestSystem()) ? request.getRequestSystem() : null);

    // set cache type
    if (request.getProperties() != null) {
      queryContext.setResultType(OpenApiProperties.getResultType(request.getProperties()));
    }

    handleOrgInfo(request.getUsername(), queryContext);
    QueryContext resContext = queryService.executeQuery(queryContext);
    return resContext.getQueryId();
  }


  private QueryResult doSyncTextQuery(TextQueryRequest request,Integer sqlType,String sql) {
    QueryContext queryContext = new QueryContext();
    queryContext.setRouterGroup(RouterGroupEnum.ADHOC.code());
    queryContext.setSql(sql);
    queryContext.setUsername(request.getUsername());
    queryContext.setEngineName(request.getSqlEngine()!=null? request.getSqlEngine() : SQLEngine.KYUUBI.getEngineName());
    queryContext.setTypeCode(QueryType.OPEN_API.getCode());
    queryContext.setDataSource(request.getDatabaseSource()!=null? request.getDatabaseSource()
            : DataBaseSource.HIVE_11.getName());

    DataBaseSource dataBaseSourceByName = DataBaseSource.getDataBaseSourceByName(request.getDatabaseSource());
    if (dataBaseSourceByName == null){
      queryContext.setClusterName(DataBaseSource.HIVE_11.getName());
    }else {
      queryContext.setClusterName(request.getDatabaseSource());
    }

    queryContext.setQueryWithTimeout(false);
    queryContext.setSqlType(sqlType);
    queryContext.setRequestSystem(apolloRefreshConfig.getRequestSystemSet().contains(request.getRequestSystem()) ? request.getRequestSystem() : null);

    // set cache type
    if (request.getProperties() != null) {
      queryContext.setResultType(OpenApiProperties.getResultType(request.getProperties()));
    }

    handleOrgInfo(request.getUsername(), queryContext);
    QueryContext queryResult = queryService.syncExecuteQuery(queryContext);

    logger.info("queryId:{} get queryContext from backend,data size : {}", queryResult.getEngineQueryId(), queryResult.getResultRowsNum());
    List<List<String>> list = queryResult.getResults();
    String[] columnNames = queryResult.getColumnNames();
    double queryTimeSecond = queryResult.getQueryTimeSecond();
    long resultSize = list == null ? 0 : list.size();
    if (!CollectionUtils.isEmpty(list)) {
      list = list.stream().skip(request.getSkip()).limit(request.getLimit()).collect(Collectors.toList());
    }
    logger.info("query result info, columns:{}, queryTimeSecond:{}", columnNames, queryTimeSecond);
    logger.info("Final state of query [{}] is: {}, elapse time: {}s", queryResult.getQueryId(), queryResult.getStatus(),
            queryResult.getQueryTimeSecond());
    QueryResult result = new QueryResult();
    result.setData(list);
    result.setTotal(resultSize);
    result.setQueryTimeInSecond(queryTimeSecond);
    result.setColumnNames(columnNames==null?null:Arrays.asList(columnNames));

    return result;
  }



  public void downloadByQueryId(DownloadRequest downloadRequest, HttpServletResponse response) {
    QueryContext queryContext = new QueryContext();
    queryContext.setQueryId(downloadRequest.getQueryId());
    switch (downloadRequest.getFileType()){
      case "csv":
        queryContext.setFileType(FileType.CSV);
        break;
      case "excel":
        queryContext.setFileType(FileType.EXCEL);
        break;
      default:
        throw new IllegalArgumentException("文件类型参数错误，文件下载只支持csv、excel类型");
    }
    if (downloadRequest.getNeedZip()){
      queryContext.setNotZip(false);
    }else {
      queryContext.setNotZip(true);
    }

    QueryContext downloadContext = queryService.getFileByQueryId(queryContext);

    try {
      DownloadFileUtil.setHeader(downloadContext.getDownloadFileName(), response);
      DownloadFileUtil.downloadByte(downloadContext.getDownloadFileBytes(), response);
    } catch (Exception e) {
      logger.error("downloadByQueryId error",e);
    }
  }

  public byte[] getFileBytesByMultipartFile(UploadFileRequest request) {
    byte[] fileBytes = new byte[0];
    try {
      fileBytes = request.getFile().getBytes();
    } catch (IOException e) {
      logger.error("getFileBytesByMultipartFile request.getFile error",e);
      throw new BusinessException("getFileBytesByMultipartFile request.getFile error");
    }
    return fileBytes;
  }

  public byte[] getFileBytesByPath(InsertByPathRequest minioFileRequest) {
    switch (minioFileRequest.getFileType()){
      case Constant.CSV_PATH_MINIIO:
        return minioUtil.downloadMinio(minioFileRequest.getFilePath());
      case Constant.CSV_PATH_MINIO_ORIGINAL:
        OssService ossService = ossServiceFactory.getOssServiceByType();
        FileSystemTypeEnum fileSystemType = ossServiceFactory.getCurrentSystemUseFileSystemType();
        return ossService.download(new MiniIOOssContext(fileSystemType, minioFileRequest.getFilePath()));
      case Constant.CSV_PATH_FASTDFS:
        FastDFSPoolUtil fastDfsPoolUtil = new FastDFSPoolUtil();
        fastDfsPoolUtil.init(TRACKER_SERVER);
        return fastDfsPoolUtil.download(minioFileRequest.getFilePath());
      default:
        throw new BusinessException("csv path_type error, just support:"+Constant.CSV_PATH_MINIIO+","+Constant.CSV_PATH_FASTDFS);
    }
  }

  @Deprecated
  // todo 临时方法，后续精准营销改为miniio，且精准营销规范元数据建表后，将废弃该方法，open-api将不支持insert同时建表
  public UploadResult uploadFileAndInsert(UploadRequest request, byte[] fileBytes) {
    QueryContext queryContext = new QueryContext();
    queryContext.setUploadTableName(request.getTableName());
    queryContext.setUsername(request.getUsername());
    // TODO: 2023/7/11 controller做参数必填校验，不应该设置默认值
    queryContext.setEngineName(request.getSqlEngine()!=null? request.getSqlEngine() : SQLEngine.KYUUBI.getEngineName());
    queryContext.setTypeCode(QueryType.OPEN_API.getCode());
      // TODO: 2023/7/11 controller做参数必填校验，不应该设置默认值
    queryContext.setDataSource(request.getDatabaseSource()!=null? request.getDatabaseSource()
            : DataBaseSource.HIVE_11.getName());
    DataBaseSource dataBaseSourceByName = DataBaseSource.getDataBaseSourceByName(request.getDatabaseSource());
      // TODO: 2023/7/11 controller做参数必填校验，不应该设置默认值
    if (dataBaseSourceByName == null){
      queryContext.setClusterName(DataBaseSource.HIVE_11.getName());
    }else {
      queryContext.setClusterName(request.getDatabaseSource());
    }
    queryContext.setQueryWithTimeout(false);
    queryContext.setSqlType(Constant.SQL_NONE_DQL_TYPE);
    queryContext.setRequestSystem(apolloRefreshConfig.getRequestSystemSet().contains(request.getRequestSystem()) ? request.getRequestSystem() : null);


    QueryContext resQueryContext = queryService.uploadCSVFileCreateTableAndInsert(queryContext, fileBytes);

    UploadResult uploadResult = new UploadResult();
    uploadResult.setDatabase("upload_file");
    uploadResult.setTable(request.getTableName());
    uploadResult.setQueryId(resQueryContext.getQueryId());
    return uploadResult;
  }

  public UploadResult insertByCsvFileBytes(InsertByCsvRequest request, byte[] fileBytes) {
    // 封装QueryContext
    QueryContext queryContext = new QueryContext();
    queryContext.setUsername(request.getUsername());
    // TODO: 2023/7/11 controller做参数必填校验，不应该设置默认值
    queryContext.setEngineName(request.getSqlEngine()!=null? request.getSqlEngine() : SQLEngine.KYUUBI.getEngineName());
    queryContext.setTypeCode(QueryType.OPEN_API.getCode());
    // TODO: 2023/7/11 controller做参数必填校验，不应该设置默认值
    queryContext.setDataSource(request.getDatabaseSource()!=null? request.getDatabaseSource()
            : DataBaseSource.HIVE_11.getName());
    DataBaseSource dataBaseSourceByName = DataBaseSource.getDataBaseSourceByName(request.getDatabaseSource());
    // TODO: 2023/7/11 controller做参数必填校验，不应该设置默认值
    if (dataBaseSourceByName == null){
      queryContext.setClusterName(DataBaseSource.HIVE_11.getName());
    }else {
      queryContext.setClusterName(request.getDatabaseSource());
    }
    queryContext.setQueryWithTimeout(false);
    queryContext.setSqlType(Constant.SQL_INSERT_TYPE);
    queryContext.setRequestSystem(apolloRefreshConfig.getRequestSystemSet().contains(request.getRequestSystem()) ? request.getRequestSystem() : null);

    QueryContext resQueryContext = queryService.insertByCsvFileBytes(
            queryContext,
            request.getDbName(),
            request.getTableName(),
            request.getCsvColumnList(),
            request.getConstantColumnList(),
            fileBytes);

    UploadResult uploadResult = new UploadResult();
    uploadResult.setDatabase(request.getDbName());
    uploadResult.setTable(request.getTableName());
    uploadResult.setQueryId(resQueryContext.getQueryId());
    return uploadResult;
  }
}
