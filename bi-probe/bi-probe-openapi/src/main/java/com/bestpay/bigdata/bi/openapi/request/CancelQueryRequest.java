package com.bestpay.bigdata.bi.openapi.request;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/9/9
 */
public class CancelQueryRequest implements Serializable {

  private String queryId;

  private String username;

  private String requestSystem;

  public String getQueryId() {
    return queryId;
  }

  public void setQueryId(String queryId) {
    this.queryId = queryId;
  }

  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  public String getRequestSystem() {
    return requestSystem;
  }

  public void setRequestSystem(String requestSystem) {
    this.requestSystem = requestSystem;
  }
}
