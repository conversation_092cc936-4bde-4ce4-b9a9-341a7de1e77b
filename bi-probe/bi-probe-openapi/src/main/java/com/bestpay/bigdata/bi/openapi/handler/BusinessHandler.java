package com.bestpay.bigdata.bi.openapi.handler;

import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import com.bestpay.bigdata.bi.openapi.bean.SessionContext;

import java.util.List;

/**
 * ClassName: BusinessHandler
 * Package: com.bestpay.bigdata.bi.openapi.handler
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/3/11 17:23
 * @Version 1.0
 */
public interface BusinessHandler
{
    void accessControlProcess(SessionContext sessionContext, List<String> sqlList);

    SQLEngine getSqlEngine();
}
