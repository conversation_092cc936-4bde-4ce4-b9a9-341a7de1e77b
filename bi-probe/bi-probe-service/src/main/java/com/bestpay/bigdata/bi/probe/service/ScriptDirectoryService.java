package com.bestpay.bigdata.bi.probe.service;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.probe.request.MoveScriptDirectoryRequest;
import com.bestpay.bigdata.bi.probe.request.ScriptDirectoryRequest;
import com.bestpay.bigdata.bi.probe.response.ScriptDirectoryVO;
import javax.servlet.http.HttpServletRequest;

/**
 * ClassName: ScriptDirectoryService
 * Package: com.bestpay.bigdata.bi.probe.service
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/9/18 17:58
 * @Version 1.0
 */
public interface ScriptDirectoryService {

    /**
     * add script Directory
     * @param scriptDirectoryRequest request
     * @return ScriptDirectoryVO
     */
    Response<ScriptDirectoryVO> addScriptDirectory(ScriptDirectoryRequest scriptDirectoryRequest, HttpServletRequest httpServletRequest);

    /**
     * update script Directory
     * @param scriptDirectoryRequest request
     * @return DashboardDirectoryVO
     */
    Response<ScriptDirectoryVO> updateScriptDirectory(ScriptDirectoryRequest scriptDirectoryRequest, HttpServletRequest httpServletRequest);


    /**
     * delete script Directory
     * @param id id
     * @return boolean
     */
    Response<Boolean> deleteScriptDirectory(Long id, HttpServletRequest httpServletRequest);


    /**
     * move directory
     * @param moveDashboardDirectoryRequest request
     * @param httpServletRequest http request
     * @return
     */
    Response<Boolean> moveScriptDirectory(MoveScriptDirectoryRequest moveDashboardDirectoryRequest, HttpServletRequest httpServletRequest);

    Response<Boolean> dataReversion();
}
