package com.bestpay.bigdata.bi.probe.response;

import com.bestpay.bigdata.bi.common.response.metaData.MetaDataTable;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022-03-22-14:24
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("数据探查-公共数据表查询返回实体类")
public class QueryTableVO implements Serializable {

    private static final long serialVersionUID = -193109626071512310L;

    //current page num
    private Integer current;

    //page size
    private Integer size;

    //response record count
    private Integer total;

    //response data
    private List<MetaDataTable> records;
}
