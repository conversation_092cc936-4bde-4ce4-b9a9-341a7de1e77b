package com.bestpay.bigdata.bi.probe.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022-03-22-14:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "数据探查查询公共数据实体类")
@Builder
public class QueryTableRequest {

    @ApiModelProperty(value = "数据源",required = true)
    String datasourceName;

    @ApiModelProperty(value = "关键字",required = true)
    String keyword;

    @ApiModelProperty(value = "当前页")
    private Integer current;

    @ApiModelProperty(value = "每页条数")
    private Integer size;
}
