package com.bestpay.bigdata.bi.probe.service.impl;

import static java.util.Objects.requireNonNull;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.bean.MoveRequest;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.CookieNameEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.CookiesUtil;
import com.bestpay.bigdata.bi.database.api.probe.ProbeScriptDBService;
import com.bestpay.bigdata.bi.database.api.probe.ProbeScriptDirectoryDAOService;
import com.bestpay.bigdata.bi.database.bean.ProbeScriptDo;
import com.bestpay.bigdata.bi.database.dao.probe.ProbeScriptDirectoryDo;
import com.bestpay.bigdata.bi.probe.request.MoveScriptDirectoryRequest;
import com.bestpay.bigdata.bi.probe.request.ScriptDirectoryRequest;
import com.bestpay.bigdata.bi.probe.response.ScriptDirectoryVO;
import com.bestpay.bigdata.bi.probe.service.ScriptDirectoryService;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ClassName: ScriptDirectoryServiceImpl
 * Package: com.bestpay.bigdata.bi.probe.service.impl
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/9/19 9:30
 * @Version 1.0
 */
@Slf4j
@Service
public class ScriptDirectoryServiceImpl implements ScriptDirectoryService {

    @Resource
    private ProbeScriptDirectoryDAOService scriptDirectoryDAOService;

    @Resource
    private AiplusService aiplusService;

    @Resource
    private ProbeScriptDBService probeScriptDBService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> dataReversion() {

        List<ProbeScriptDo> allList = probeScriptDBService.getAllList();

        Map<String, List<ProbeScriptDo>> map = allList.stream().collect(Collectors.groupingBy(ProbeScriptDo::getUserName));

        Date date = new Date();
        for (String username : map.keySet()) {
            ProbeScriptDirectoryDo scriptDirectoryDo = new ProbeScriptDirectoryDo();
            scriptDirectoryDo.setName("我的脚本");
            scriptDirectoryDo.setDirSort(1L);
            scriptDirectoryDo.setStatusCode(0);
            scriptDirectoryDo.setCreatedBy(username);
            scriptDirectoryDo.setUpdatedBy(username);
            scriptDirectoryDo.setCreatedAt(date);
            scriptDirectoryDo.setUpdatedAt(date);

            List<ProbeScriptDo> probeScriptDos = map.get(username);

            if (CollectionUtils.isNotEmpty(probeScriptDos)) {
                scriptDirectoryDo = scriptDirectoryDAOService.insertDb(scriptDirectoryDo);
                probeScriptDos = probeScriptDos.stream()
                    .sorted(Comparator.comparing(ProbeScriptDo::getCreatedAt))
                    .collect(Collectors.toList());
                long orderNum = 1;
                for (ProbeScriptDo probeScriptDo : probeScriptDos) {
                    probeScriptDo.setDirId(scriptDirectoryDo.getId());
                    probeScriptDo.setOrderNum(orderNum);
                    orderNum++;
                    probeScriptDBService.updateById(probeScriptDo);
                }
            }
        }

        return null;
    }

    /**
     * add script Directory
     *
     * @param scriptDirectoryRequest request
     * @param httpServletRequest
     * @return ScriptDirectoryVO
     */
    @Override
    public Response<ScriptDirectoryVO> addScriptDirectory(ScriptDirectoryRequest scriptDirectoryRequest, HttpServletRequest httpServletRequest) {
        log.info("add ScriptDirectory request : {}", JSONUtil.toJsonStr(scriptDirectoryRequest));

        // add db
        UserInfo userInfo = getUserInfoFromHttpRequest(httpServletRequest);

        // check param
        checkRequestParam(scriptDirectoryRequest, userInfo.getEmail());

        Long maxDirSort = scriptDirectoryDAOService.getMaxDirSort(userInfo.getEmail());
        Long nextSort = Optional.ofNullable(maxDirSort).orElse(0L)+1;

        log.info("maxDirSort : {}", maxDirSort);

        ProbeScriptDirectoryDo scriptDirectoryDo = new ProbeScriptDirectoryDo();
        scriptDirectoryDo.setName(scriptDirectoryRequest.getName());
        scriptDirectoryDo.setDirSort(nextSort);
        scriptDirectoryDo.setStatusCode(StatusCodeEnum.ONLINE.getCode());
        scriptDirectoryDo.setCreatedAt(new Date());
        scriptDirectoryDo.setCreatedBy(userInfo.getEmail());
        scriptDirectoryDo.setUpdatedAt(new Date());
        scriptDirectoryDo.setUpdatedBy(userInfo.getEmail());
        scriptDirectoryDo.setOrgCode(userInfo.getOrg().getCode());
        scriptDirectoryDo = scriptDirectoryDAOService.insertDb(scriptDirectoryDo);

        // return to front
        ScriptDirectoryVO scriptDirectoryVO = new ScriptDirectoryVO();
        BeanUtils.copyProperties(scriptDirectoryDo, scriptDirectoryVO);

        return Response.ok(scriptDirectoryVO);
    }

    private void checkRequestParam(ScriptDirectoryRequest scriptDirectoryRequest, String createdBy) {
        String name = scriptDirectoryRequest.getName();
        if (StringUtils.isEmpty(name)) {
            throw new BusinessException(CodeEnum.SCRIPT_DIRECTORY_NAME_NULL_ERROR);
        }
        if (name.length() > 50) {
            throw new BusinessException(CodeEnum.SCRIPT_DIRECTORY_NAME_LENGTH_ERROR);
        }

        List<ProbeScriptDirectoryDo> list = scriptDirectoryDAOService.getDirectoryListByCreatedBy(createdBy);
        Optional<ProbeScriptDirectoryDo> first = list.stream()
                .filter(dir -> dir.getName().equals(scriptDirectoryRequest.getName())).findFirst();
        if (first.isPresent()) {
            throw new BusinessException(CodeEnum.SCRIPT_DIRECTORY_SAME_NAME_ERROR);
        }
    }

    /**
     * update script Directory
     *
     * @param scriptDirectoryRequest request
     * @param httpServletRequest
     * @return DashboardDirectoryVO
     */
    @Override
    public Response<ScriptDirectoryVO> updateScriptDirectory(ScriptDirectoryRequest scriptDirectoryRequest, HttpServletRequest httpServletRequest) {
        log.info("update ScriptDirectory : {}", JSONUtil.toJsonStr(scriptDirectoryRequest));
        UserInfo userInfo = getUserInfoFromHttpRequest(httpServletRequest);

        // check param
        requireNonNull(scriptDirectoryRequest.getId(), "id can not be null");

        ProbeScriptDirectoryDo directoryDo = scriptDirectoryDAOService.queryDbById(scriptDirectoryRequest.getId());
        if (!directoryDo.getName().equals(scriptDirectoryRequest.getName())) {
            checkRequestParam(scriptDirectoryRequest, userInfo.getEmail());
        }


        ProbeScriptDirectoryDo scriptDirectoryDo = new ProbeScriptDirectoryDo();
        scriptDirectoryDo.setId(scriptDirectoryRequest.getId());
        scriptDirectoryDo.setName(scriptDirectoryRequest.getName());
        scriptDirectoryDo.setUpdatedAt(new Date());
        scriptDirectoryDo.setUpdatedBy(userInfo.getEmail());

        ProbeScriptDirectoryDo updatedDirectoryDo = scriptDirectoryDAOService.updateDbById(scriptDirectoryDo);

        // return to front
        ScriptDirectoryVO scriptDirectoryVO = new ScriptDirectoryVO();
        BeanUtils.copyProperties(updatedDirectoryDo, scriptDirectoryVO);

        return Response.ok(scriptDirectoryVO);
    }

    /**
     * delete script Directory
     *
     * @param id                 id
     * @param httpServletRequest
     * @return boolean
     */
    @Override
    public Response<Boolean> deleteScriptDirectory(Long id, HttpServletRequest httpServletRequest) {
        log.info("delete ScriptDirectory : {}", id);

        // check script directory is any empty
        List<ProbeScriptDo> probeScriptDos = probeScriptDBService.getListByDirId(id);
        if (CollectionUtils.isNotEmpty(probeScriptDos)) {
            throw new BusinessException(CodeEnum.SCRIPT_DIRECTORY_NOT_EMPTY_ERROR);
        }

        // add db
        UserInfo userInfo = getUserInfoFromHttpRequest(httpServletRequest);

        ProbeScriptDirectoryDo scriptDirectoryDo = new ProbeScriptDirectoryDo();
        scriptDirectoryDo.setId(id);
        scriptDirectoryDo.setStatusCode(StatusCodeEnum.DELETE.getCode());
        scriptDirectoryDo.setUpdatedAt(new Date());
        scriptDirectoryDo.setUpdatedBy(userInfo.getEmail());

        scriptDirectoryDAOService.updateDbById(scriptDirectoryDo);

        return Response.ok(true);
    }

    /**
     * move directory
     *
     * @param moveScriptDirectoryRequest request
     * @param httpServletRequest            http request
     * @return
     */
    @Override
    public Response<Boolean> moveScriptDirectory(MoveScriptDirectoryRequest moveScriptDirectoryRequest,
                                                 HttpServletRequest httpServletRequest) {
        // param check
        List<MoveRequest> postMoveSortList = moveScriptDirectoryRequest.getPostMoveSortList();

        if (CollectionUtils.isEmpty(postMoveSortList)) {
            return Response.error(CodeEnum.DASHBOARD_DIRECTORY_MOVE_PARAM_ERROR);
        }

        log.info("moveScriptDirectory postMoveSortList : {}", postMoveSortList);
        UserInfo userInfo = getUserInfoFromHttpRequest(httpServletRequest);

        // move
        for (int i = 0; i < postMoveSortList.size(); i++) {
            ProbeScriptDirectoryDo scriptDirectoryDo = new ProbeScriptDirectoryDo();
            scriptDirectoryDo.setId(postMoveSortList.get(i).getId());
            scriptDirectoryDo.setDirSort(postMoveSortList.get(i).getOrderNum());
            scriptDirectoryDAOService.updateDbById(scriptDirectoryDo);
        }

        return Response.ok(true);
    }


//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public Response<Boolean> dataReversion() {
//
//        List<ProbeScriptDo> allList = probeScriptDBService.getAllList();
//
//        Map<String, List<ProbeScriptDo>> map = allList.stream().collect(Collectors.groupingBy(ProbeScriptDo::getUserName));
//
//        Date date = new Date();
//        for (String username : map.keySet()) {
//            ProbeScriptDirectoryDo scriptDirectoryDo = new ProbeScriptDirectoryDo();
//            scriptDirectoryDo.setName("我的脚本");
//            scriptDirectoryDo.setDirSort(1l);
//            scriptDirectoryDo.setStatusCode(0);
//            scriptDirectoryDo.setCreatedBy(username);
//            scriptDirectoryDo.setUpdatedBy(username);
//            scriptDirectoryDo.setCreatedAt(date);
//            scriptDirectoryDo.setUpdatedAt(date);
//
//            List<ProbeScriptDo> probeScriptDos = map.get(username);
//
//            if (CollectionUtils.isNotEmpty(probeScriptDos)) {
//                scriptDirectoryDo = scriptDirectoryDAOService.insertDb(scriptDirectoryDo);
//                probeScriptDos = probeScriptDos.stream()
//                        .sorted(Comparator.comparing(ProbeScriptDo::getCreatedAt))
//                        .collect(Collectors.toList());
//                long orderNum = 1;
//                for (ProbeScriptDo probeScriptDo : probeScriptDos) {
//                    probeScriptDo.setDirId(scriptDirectoryDo.getId());
//                    probeScriptDo.setOrderNum(orderNum);
//                    orderNum++;
//                    probeScriptDBService.updateById(probeScriptDo);
//                }
//            }
//        }
//
//        return null;
//    }

    private UserInfo getUserInfoFromHttpRequest(HttpServletRequest httpServletRequest) {
        String cookieValue = CookiesUtil.getCookieValue(httpServletRequest.getCookies(),
                CookieNameEnum.BIGDATA_AI_PLUS_USER_ID.code());
        return aiplusService.getUserInfo(cookieValue);
    }
}
