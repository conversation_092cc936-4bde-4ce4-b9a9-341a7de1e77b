package com.bestpay.bigdata.bi.probe.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023-03-02-15:29
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthorityDatabaseRequest implements Serializable{

    /** 当前用户名 */
    private String username;

    /** 数据源名称 */
    private String datasourceName;
}
